{"version": 2, "name": "kontour-coin", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"SOLANA_RPC_URL": "https://api.devnet.solana.com", "NEXT_PUBLIC_API_URL": "https://kontour-coin.vercel.app/api"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"pages/api/**/*.ts": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}]}