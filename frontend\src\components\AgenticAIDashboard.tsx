import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface Agent {
  id: string;
  name: string;
  type: 'coordinator' | 'specialist' | 'validator' | 'optimizer' | 'learner';
  status: 'idle' | 'active' | 'training' | 'error';
  performance: number;
  tasksCompleted: number;
  knowledgeScore: number;
  lastActive: string;
  capabilities: string[];
}

interface Task {
  id: string;
  title: string;
  agentId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  progress: number;
  createdAt: string;
  estimatedCompletion: string;
}

interface TrainingSession {
  id: string;
  agentId: string;
  type: 'supervised' | 'unsupervised' | 'reinforcement' | 'transfer';
  status: 'running' | 'completed' | 'paused' | 'failed';
  progress: number;
  accuracy: number;
  loss: number;
  startTime: string;
  estimatedEnd: string;
}

const AgenticAIDashboard: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [trainingSessions, setTrainingSessions] = useState<TrainingSession[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [systemMetrics, setSystemMetrics] = useState({
    totalAgents: 0,
    activeAgents: 0,
    completedTasks: 0,
    averagePerformance: 0,
    systemLoad: 0
  });

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch agents
      const agentsResponse = await fetch('/api/agentic/agents/list');
      const agentsData = await agentsResponse.json();
      setAgents(agentsData.agents || []);

      // Fetch tasks
      const tasksResponse = await fetch('/api/agentic/tasks/active');
      const tasksData = await tasksResponse.json();
      setTasks(tasksData.tasks || []);

      // Fetch training sessions
      const trainingResponse = await fetch('/api/agentic/training/sessions');
      const trainingData = await trainingResponse.json();
      setTrainingSessions(trainingData.sessions || []);

      // Update system metrics
      updateSystemMetrics(agentsData.agents || [], tasksData.tasks || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const updateSystemMetrics = (agentsList: Agent[], tasksList: Task[]) => {
    const activeAgents = agentsList.filter(agent => agent.status === 'active').length;
    const completedTasks = tasksList.filter(task => task.status === 'completed').length;
    const avgPerformance = agentsList.length > 0 
      ? agentsList.reduce((sum, agent) => sum + agent.performance, 0) / agentsList.length 
      : 0;

    setSystemMetrics({
      totalAgents: agentsList.length,
      activeAgents,
      completedTasks,
      averagePerformance: Math.round(avgPerformance * 100) / 100,
      systemLoad: Math.random() * 100 // Simulated system load
    });
  };

  const createNewAgent = async (agentType: string) => {
    try {
      const response = await fetch('/api/agentic/agents/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: `agent_${Date.now()}`,
          agent_type: agentType,
          name: `${agentType.charAt(0).toUpperCase() + agentType.slice(1)} Agent`,
          capabilities: getDefaultCapabilities(agentType),
          goals: []
        })
      });

      if (response.ok) {
        fetchDashboardData();
      }
    } catch (error) {
      console.error('Error creating agent:', error);
    }
  };

  const getDefaultCapabilities = (type: string): string[] => {
    const capabilities = {
      coordinator: ['task_delegation', 'resource_management', 'conflict_resolution'],
      specialist: ['domain_expertise', 'problem_solving', 'analysis'],
      validator: ['quality_assurance', 'verification', 'compliance_check'],
      optimizer: ['performance_tuning', 'resource_optimization', 'efficiency_analysis'],
      learner: ['knowledge_acquisition', 'pattern_recognition', 'adaptation']
    };
    return capabilities[type as keyof typeof capabilities] || [];
  };

  const assignTask = async (agentId: string, taskTitle: string) => {
    try {
      const response = await fetch(`/api/agentic/agents/${agentId}/assign-task`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          task_id: `task_${Date.now()}`,
          agent_id: agentId,
          description: taskTitle,
          parameters: {},
          priority: 5
        })
      });

      if (response.ok) {
        fetchDashboardData();
      }
    } catch (error) {
      console.error('Error assigning task:', error);
    }
  };

  const startTraining = async (agentId: string, trainingType: string) => {
    try {
      const response = await fetch('/api/agentic/training/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: agentId,
          training_type: trainingType,
          parameters: {
            epochs: 100,
            learning_rate: 0.001,
            batch_size: 32
          }
        })
      });

      if (response.ok) {
        fetchDashboardData();
      }
    } catch (error) {
      console.error('Error starting training:', error);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      idle: 'bg-gray-100 text-gray-800',
      training: 'bg-blue-100 text-blue-800',
      error: 'bg-red-100 text-red-800',
      running: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      pending: 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">🤖 Agentic AI Dashboard</h1>
        <Badge variant="outline" className="text-blue-600">
          {systemMetrics.activeAgents} Active Agents
        </Badge>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{systemMetrics.totalAgents}</div>
            <div className="text-sm text-gray-600">Total Agents</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{systemMetrics.activeAgents}</div>
            <div className="text-sm text-gray-600">Active Agents</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{systemMetrics.completedTasks}</div>
            <div className="text-sm text-gray-600">Completed Tasks</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{systemMetrics.averagePerformance}</div>
            <div className="text-sm text-gray-600">Avg Performance</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{Math.round(systemMetrics.systemLoad)}%</div>
            <div className="text-sm text-gray-600">System Load</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="agents" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Agent Management</h2>
            <div className="space-x-2">
              {['coordinator', 'specialist', 'validator', 'optimizer', 'learner'].map(type => (
                <Button
                  key={type}
                  onClick={() => createNewAgent(type)}
                  variant="outline"
                  size="sm"
                >
                  Create {type.charAt(0).toUpperCase() + type.slice(1)}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agents.map(agent => (
              <Card key={agent.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <Badge className={getStatusColor(agent.status)}>
                      {agent.status}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600">{agent.type}</div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Performance:</span>
                      <span className="text-sm font-medium">{(agent.performance * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Tasks Completed:</span>
                      <span className="text-sm font-medium">{agent.tasksCompleted}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Knowledge Score:</span>
                      <span className="text-sm font-medium">{(agent.knowledgeScore * 100).toFixed(1)}%</span>
                    </div>
                    <div className="mt-3 space-x-2">
                      <Button
                        size="sm"
                        onClick={() => assignTask(agent.id, 'New Analysis Task')}
                      >
                        Assign Task
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => startTraining(agent.id, 'reinforcement')}
                      >
                        Train
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <h2 className="text-xl font-semibold">Task Management</h2>
          <div className="space-y-2">
            {tasks.map(task => (
              <Card key={task.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">{task.title}</h3>
                      <p className="text-sm text-gray-600">Agent: {task.agentId}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(task.status)}>
                        {task.status}
                      </Badge>
                      <div className="text-sm text-gray-600">{task.progress}%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <h2 className="text-xl font-semibold">Training Sessions</h2>
          <div className="space-y-2">
            {trainingSessions.map(session => (
              <Card key={session.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">{session.type} Training</h3>
                      <p className="text-sm text-gray-600">Agent: {session.agentId}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-sm">
                        <div>Progress: {session.progress}%</div>
                        <div>Accuracy: {(session.accuracy * 100).toFixed(1)}%</div>
                      </div>
                      <Badge className={getStatusColor(session.status)}>
                        {session.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <h2 className="text-xl font-semibold">Performance Analytics</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Agent Performance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={agents.map(agent => ({
                    name: agent.name,
                    performance: agent.performance * 100,
                    knowledge: agent.knowledgeScore * 100
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="performance" stroke="#8884d8" />
                    <Line type="monotone" dataKey="knowledge" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Agent Type Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={Object.entries(
                        agents.reduce((acc, agent) => {
                          acc[agent.type] = (acc[agent.type] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map(([type, count]) => ({ name: type, value: count }))}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {Object.entries(
                        agents.reduce((acc, agent) => {
                          acc[agent.type] = (acc[agent.type] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AgenticAIDashboard;
