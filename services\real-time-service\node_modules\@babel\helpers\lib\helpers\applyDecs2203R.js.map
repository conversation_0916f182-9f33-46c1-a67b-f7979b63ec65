{"version": 3, "names": ["_setFunctionName", "require", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyDecs2203RFactory", "createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "kindStr", "ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "static", "private", "v", "get", "set", "call", "access", "fnName", "Error", "fn", "hint", "TypeError", "assertValidReturnValue", "type", "undefined", "init", "applyMemberDec", "ret", "base", "decInfo", "decs", "prefix", "setFunctionName", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2203R", "memberDecs", "e", "c", "exports", "default"], "sources": ["../../src/helpers/applyDecs2203R.js"], "sourcesContent": ["/* @minVersion 7.20.0 */\n/* @onlyBabel7 */\n\nimport setFunctionName from \"setFunctionName\";\nimport toPropertyKey from \"toPropertyKey\";\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction applyDecs2203RFactory() {\n  function createAddInitializerMethod(initializers, decoratorFinishedRef) {\n    return function addInitializer(initializer) {\n      assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n      assertCallable(initializer, \"An initializer\");\n      initializers.push(initializer);\n    };\n  }\n\n  function memberDec(\n    dec,\n    name,\n    desc,\n    initializers,\n    kind,\n    isStatic,\n    isPrivate,\n    value,\n  ) {\n    var kindStr;\n\n    switch (kind) {\n      case 1 /* ACCESSOR */:\n        kindStr = \"accessor\";\n        break;\n      case 2 /* METHOD */:\n        kindStr = \"method\";\n        break;\n      case 3 /* GETTER */:\n        kindStr = \"getter\";\n        break;\n      case 4 /* SETTER */:\n        kindStr = \"setter\";\n        break;\n      default:\n        kindStr = \"field\";\n    }\n\n    var ctx = {\n      kind: kindStr,\n      name: isPrivate ? \"#\" + name : toPropertyKey(name),\n      static: isStatic,\n      private: isPrivate,\n    };\n\n    var decoratorFinishedRef = { v: false };\n\n    if (kind !== 0 /* FIELD */) {\n      ctx.addInitializer = createAddInitializerMethod(\n        initializers,\n        decoratorFinishedRef,\n      );\n    }\n\n    var get, set;\n    if (kind === 0 /* FIELD */) {\n      if (isPrivate) {\n        get = desc.get;\n        set = desc.set;\n      } else {\n        get = function () {\n          return this[name];\n        };\n        set = function (v) {\n          this[name] = v;\n        };\n      }\n    } else if (kind === 2 /* METHOD */) {\n      get = function () {\n        return desc.value;\n      };\n    } else {\n      // replace with values that will go through the final getter and setter\n      if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n        get = function () {\n          return desc.get.call(this);\n        };\n      }\n\n      if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n        set = function (v) {\n          desc.set.call(this, v);\n        };\n      }\n    }\n    ctx.access =\n      get && set ? { get: get, set: set } : get ? { get: get } : { set: set };\n\n    try {\n      return dec(value, ctx);\n    } finally {\n      decoratorFinishedRef.v = true;\n    }\n  }\n\n  function assertNotFinished(decoratorFinishedRef, fnName) {\n    if (decoratorFinishedRef.v) {\n      throw new Error(\n        \"attempted to call \" + fnName + \" after decoration was finished\",\n      );\n    }\n  }\n\n  function assertCallable(fn, hint) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(hint + \" must be a function\");\n    }\n  }\n\n  function assertValidReturnValue(kind, value) {\n    var type = typeof value;\n\n    if (kind === 1 /* ACCESSOR */) {\n      if (type !== \"object\" || value === null) {\n        throw new TypeError(\n          \"accessor decorators must return an object with get, set, or init properties or void 0\",\n        );\n      }\n      if (value.get !== undefined) {\n        assertCallable(value.get, \"accessor.get\");\n      }\n      if (value.set !== undefined) {\n        assertCallable(value.set, \"accessor.set\");\n      }\n      if (value.init !== undefined) {\n        assertCallable(value.init, \"accessor.init\");\n      }\n    } else if (type !== \"function\") {\n      var hint;\n      if (kind === 0 /* FIELD */) {\n        hint = \"field\";\n      } else if (kind === 10 /* CLASS */) {\n        hint = \"class\";\n      } else {\n        hint = \"method\";\n      }\n      throw new TypeError(\n        hint + \" decorators must return a function or void 0\",\n      );\n    }\n  }\n\n  function applyMemberDec(\n    ret,\n    base,\n    decInfo,\n    name,\n    kind,\n    isStatic,\n    isPrivate,\n    initializers,\n  ) {\n    var decs = decInfo[0];\n\n    var desc, init, prefix, value;\n\n    if (isPrivate) {\n      if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n        desc = {\n          get: decInfo[3],\n          set: decInfo[4],\n        };\n        prefix = \"get\";\n      } else if (kind === 3 /* GETTER */) {\n        desc = {\n          get: decInfo[3],\n        };\n        prefix = \"get\";\n      } else if (kind === 4 /* SETTER */) {\n        desc = {\n          set: decInfo[3],\n        };\n        prefix = \"set\";\n      } else {\n        desc = {\n          value: decInfo[3],\n        };\n      }\n      if (kind !== 0 /* FIELD */) {\n        if (kind === 1 /* ACCESSOR */) {\n          setFunctionName(decInfo[4], \"#\" + name, \"set\");\n        }\n        setFunctionName(decInfo[3], \"#\" + name, prefix);\n      }\n    } else if (kind !== 0 /* FIELD */) {\n      desc = Object.getOwnPropertyDescriptor(base, name);\n    }\n\n    if (kind === 1 /* ACCESSOR */) {\n      value = {\n        get: desc.get,\n        set: desc.set,\n      };\n    } else if (kind === 2 /* METHOD */) {\n      value = desc.value;\n    } else if (kind === 3 /* GETTER */) {\n      value = desc.get;\n    } else if (kind === 4 /* SETTER */) {\n      value = desc.set;\n    }\n\n    var newValue, get, set;\n\n    if (typeof decs === \"function\") {\n      newValue = memberDec(\n        decs,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n\n        if (kind === 0 /* FIELD */) {\n          init = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          init = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n      }\n    } else {\n      for (var i = decs.length - 1; i >= 0; i--) {\n        var dec = decs[i];\n\n        newValue = memberDec(\n          dec,\n          name,\n          desc,\n          initializers,\n          kind,\n          isStatic,\n          isPrivate,\n          value,\n        );\n\n        if (newValue !== void 0) {\n          assertValidReturnValue(kind, newValue);\n          var newInit;\n\n          if (kind === 0 /* FIELD */) {\n            newInit = newValue;\n          } else if (kind === 1 /* ACCESSOR */) {\n            newInit = newValue.init;\n            get = newValue.get || value.get;\n            set = newValue.set || value.set;\n\n            value = { get: get, set: set };\n          } else {\n            value = newValue;\n          }\n\n          if (newInit !== void 0) {\n            if (init === void 0) {\n              init = newInit;\n            } else if (typeof init === \"function\") {\n              init = [init, newInit];\n            } else {\n              init.push(newInit);\n            }\n          }\n        }\n      }\n    }\n\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      if (init === void 0) {\n        // If the initializer was void 0, sub in a dummy initializer\n        init = function (instance, init) {\n          return init;\n        };\n      } else if (typeof init !== \"function\") {\n        var ownInitializers = init;\n\n        init = function (instance, init) {\n          var value = init;\n\n          for (var i = 0; i < ownInitializers.length; i++) {\n            value = ownInitializers[i].call(instance, value);\n          }\n\n          return value;\n        };\n      } else {\n        var originalInitializer = init;\n\n        init = function (instance, init) {\n          return originalInitializer.call(instance, init);\n        };\n      }\n\n      ret.push(init);\n    }\n\n    if (kind !== 0 /* FIELD */) {\n      if (kind === 1 /* ACCESSOR */) {\n        desc.get = value.get;\n        desc.set = value.set;\n      } else if (kind === 2 /* METHOD */) {\n        desc.value = value;\n      } else if (kind === 3 /* GETTER */) {\n        desc.get = value;\n      } else if (kind === 4 /* SETTER */) {\n        desc.set = value;\n      }\n\n      if (isPrivate) {\n        if (kind === 1 /* ACCESSOR */) {\n          ret.push(function (instance, args) {\n            return value.get.call(instance, args);\n          });\n          ret.push(function (instance, args) {\n            return value.set.call(instance, args);\n          });\n        } else if (kind === 2 /* METHOD */) {\n          ret.push(value);\n        } else {\n          ret.push(function (instance, args) {\n            return value.call(instance, args);\n          });\n        }\n      } else {\n        Object.defineProperty(base, name, desc);\n      }\n    }\n  }\n\n  function applyMemberDecs(Class, decInfos) {\n    var ret = [];\n    var protoInitializers;\n    var staticInitializers;\n\n    var existingProtoNonFields = new Map();\n    var existingStaticNonFields = new Map();\n\n    for (var i = 0; i < decInfos.length; i++) {\n      var decInfo = decInfos[i];\n\n      // skip computed property names\n      if (!Array.isArray(decInfo)) continue;\n\n      var kind = decInfo[1];\n      var name = decInfo[2];\n      var isPrivate = decInfo.length > 3;\n\n      var isStatic = kind >= 5; /* STATIC */\n      var base;\n      var initializers;\n\n      if (isStatic) {\n        base = Class;\n        kind = kind - 5 /* STATIC */;\n        // initialize staticInitializers when we see a non-field static member\n        if (kind !== 0 /* FIELD */) {\n          staticInitializers = staticInitializers || [];\n          initializers = staticInitializers;\n        }\n      } else {\n        base = Class.prototype;\n        // initialize protoInitializers when we see a non-field member\n        if (kind !== 0 /* FIELD */) {\n          protoInitializers = protoInitializers || [];\n          initializers = protoInitializers;\n        }\n      }\n\n      if (kind !== 0 /* FIELD */ && !isPrivate) {\n        var existingNonFields = isStatic\n          ? existingStaticNonFields\n          : existingProtoNonFields;\n\n        var existingKind = existingNonFields.get(name) || 0;\n\n        if (\n          existingKind === true ||\n          (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n          (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n        ) {\n          throw new Error(\n            \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n              name,\n          );\n        } else if (!existingKind && kind > 2 /* METHOD */) {\n          existingNonFields.set(name, kind);\n        } else {\n          existingNonFields.set(name, true);\n        }\n      }\n\n      applyMemberDec(\n        ret,\n        base,\n        decInfo,\n        name,\n        kind,\n        isStatic,\n        isPrivate,\n        initializers,\n      );\n    }\n\n    pushInitializers(ret, protoInitializers);\n    pushInitializers(ret, staticInitializers);\n    return ret;\n  }\n\n  function pushInitializers(ret, initializers) {\n    if (initializers) {\n      ret.push(function (instance) {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(instance);\n        }\n        return instance;\n      });\n    }\n  }\n\n  function applyClassDecs(targetClass, classDecs) {\n    if (classDecs.length > 0) {\n      var initializers = [];\n      var newClass = targetClass;\n      var name = targetClass.name;\n\n      for (var i = classDecs.length - 1; i >= 0; i--) {\n        var decoratorFinishedRef = { v: false };\n\n        try {\n          var nextNewClass = classDecs[i](newClass, {\n            kind: \"class\",\n            name: name,\n            addInitializer: createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef,\n            ),\n          });\n        } finally {\n          decoratorFinishedRef.v = true;\n        }\n\n        if (nextNewClass !== undefined) {\n          assertValidReturnValue(10 /* CLASS */, nextNewClass);\n          newClass = nextNewClass;\n        }\n      }\n\n      return [\n        newClass,\n        function () {\n          for (var i = 0; i < initializers.length; i++) {\n            initializers[i].call(newClass);\n          }\n        },\n      ];\n    }\n    // The transformer will not emit assignment when there are no class decorators,\n    // so we don't have to return an empty array here.\n  }\n\n  /**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\n  return function applyDecs2203R(targetClass, memberDecs, classDecs) {\n    return {\n      e: applyMemberDecs(targetClass, memberDecs),\n      // Lazily apply class decorations so that member init locals can be properly bound.\n      get c() {\n        return applyClassDecs(targetClass, classDecs);\n      },\n    };\n  };\n}\n\nexport default function applyDecs2203R(targetClass, memberDecs, classDecs) {\n  return (applyDecs2203R = applyDecs2203RFactory())(\n    targetClass,\n    memberDecs,\n    classDecs,\n  );\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAoBA,SAASE,qBAAqBA,CAAA,EAAG;EAC/B,SAASC,0BAA0BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;IACtE,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAE;MAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;MACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;MAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;IAChC,CAAC;EACH;EAEA,SAASI,SAASA,CAChBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACL;IACA,IAAIC,OAAO;IAEX,QAAQJ,IAAI;MACV,KAAK,CAAC;QACJI,OAAO,GAAG,UAAU;QACpB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF;QACEA,OAAO,GAAG,OAAO;IACrB;IAEA,IAAIC,GAAG,GAAG;MACRL,IAAI,EAAEI,OAAO;MACbN,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGQ,cAAa,CAACR,IAAI,CAAC;MAClDS,MAAM,EAAEN,QAAQ;MAChBO,OAAO,EAAEN;IACX,CAAC;IAED,IAAIZ,oBAAoB,GAAG;MAAEmB,CAAC,EAAE;IAAM,CAAC;IAEvC,IAAIT,IAAI,KAAK,CAAC,EAAc;MAC1BK,GAAG,CAACd,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBACF,CAAC;IACH;IAEA,IAAIoB,GAAG,EAAEC,GAAG;IACZ,IAAIX,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIE,SAAS,EAAE;QACbQ,GAAG,GAAGX,IAAI,CAACW,GAAG;QACdC,GAAG,GAAGZ,IAAI,CAACY,GAAG;MAChB,CAAC,MAAM;QACLD,GAAG,GAAG,SAAAA,CAAA,EAAY;UAChB,OAAO,IAAI,CAACZ,IAAI,CAAC;QACnB,CAAC;QACDa,GAAG,GAAG,SAAAA,CAAUF,CAAC,EAAE;UACjB,IAAI,CAACX,IAAI,CAAC,GAAGW,CAAC;QAChB,CAAC;MACH;IACF,CAAC,MAAM,IAAIT,IAAI,KAAK,CAAC,EAAe;MAClCU,GAAG,GAAG,SAAAA,CAAA,EAAY;QAChB,OAAOX,IAAI,CAACI,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIH,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxDU,GAAG,GAAG,SAAAA,CAAA,EAAY;UAChB,OAAOX,IAAI,CAACW,GAAG,CAACE,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;MACH;MAEA,IAAIZ,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxDW,GAAG,GAAG,SAAAA,CAAUF,CAAC,EAAE;UACjBV,IAAI,CAACY,GAAG,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;QACxB,CAAC;MACH;IACF;IACAJ,GAAG,CAACQ,MAAM,GACRH,GAAG,IAAIC,GAAG,GAAG;MAAED,GAAG,EAAEA,GAAG;MAAEC,GAAG,EAAEA;IAAI,CAAC,GAAGD,GAAG,GAAG;MAAEA,GAAG,EAAEA;IAAI,CAAC,GAAG;MAAEC,GAAG,EAAEA;IAAI,CAAC;IAEzE,IAAI;MACF,OAAOd,GAAG,CAACM,KAAK,EAAEE,GAAG,CAAC;IACxB,CAAC,SAAS;MACRf,oBAAoB,CAACmB,CAAC,GAAG,IAAI;IAC/B;EACF;EAEA,SAAShB,iBAAiBA,CAACH,oBAAoB,EAAEwB,MAAM,EAAE;IACvD,IAAIxB,oBAAoB,CAACmB,CAAC,EAAE;MAC1B,MAAM,IAAIM,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;IACH;EACF;EAEA,SAASpB,cAAcA,CAACsB,EAAE,EAAEC,IAAI,EAAE;IAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIE,SAAS,CAACD,IAAI,GAAG,qBAAqB,CAAC;IACnD;EACF;EAEA,SAASE,sBAAsBA,CAACnB,IAAI,EAAEG,KAAK,EAAE;IAC3C,IAAIiB,IAAI,GAAG,OAAOjB,KAAK;IAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;MAC7B,IAAIoB,IAAI,KAAK,QAAQ,IAAIjB,KAAK,KAAK,IAAI,EAAE;QACvC,MAAM,IAAIe,SAAS,CACjB,uFACF,CAAC;MACH;MACA,IAAIf,KAAK,CAACO,GAAG,KAAKW,SAAS,EAAE;QAC3B3B,cAAc,CAACS,KAAK,CAACO,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIP,KAAK,CAACQ,GAAG,KAAKU,SAAS,EAAE;QAC3B3B,cAAc,CAACS,KAAK,CAACQ,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIR,KAAK,CAACmB,IAAI,KAAKD,SAAS,EAAE;QAC5B3B,cAAc,CAACS,KAAK,CAACmB,IAAI,EAAE,eAAe,CAAC;MAC7C;IACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIH,IAAI;MACR,IAAIjB,IAAI,KAAK,CAAC,EAAc;QAC1BiB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAIjB,IAAI,KAAK,EAAE,EAAc;QAClCiB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACLA,IAAI,GAAG,QAAQ;MACjB;MACA,MAAM,IAAIC,SAAS,CACjBD,IAAI,GAAG,8CACT,CAAC;IACH;EACF;EAEA,SAASM,cAAcA,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP5B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTb,YAAY,EACZ;IACA,IAAIsC,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;IAErB,IAAI3B,IAAI,EAAEuB,IAAI,EAAEM,MAAM,EAAEzB,KAAK;IAE7B,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;QACvDD,IAAI,GAAG;UACLW,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC;UACff,GAAG,EAAEe,OAAO,CAAC,CAAC;QAChB,CAAC;QACDE,MAAM,GAAG,KAAK;MAChB,CAAC,MAAM,IAAI5B,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLW,GAAG,EAAEgB,OAAO,CAAC,CAAC;QAChB,CAAC;QACDE,MAAM,GAAG,KAAK;MAChB,CAAC,MAAM,IAAI5B,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLY,GAAG,EAAEe,OAAO,CAAC,CAAC;QAChB,CAAC;QACDE,MAAM,GAAG,KAAK;MAChB,CAAC,MAAM;QACL7B,IAAI,GAAG;UACLI,KAAK,EAAEuB,OAAO,CAAC,CAAC;QAClB,CAAC;MACH;MACA,IAAI1B,IAAI,KAAK,CAAC,EAAc;QAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;UAC7B6B,gBAAe,CAACH,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG5B,IAAI,EAAE,KAAK,CAAC;QAChD;QACA+B,gBAAe,CAACH,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG5B,IAAI,EAAE8B,MAAM,CAAC;MACjD;IACF,CAAC,MAAM,IAAI5B,IAAI,KAAK,CAAC,EAAc;MACjCD,IAAI,GAAG+B,MAAM,CAACC,wBAAwB,CAACN,IAAI,EAAE3B,IAAI,CAAC;IACpD;IAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;MAC7BG,KAAK,GAAG;QACNO,GAAG,EAAEX,IAAI,CAACW,GAAG;QACbC,GAAG,EAAEZ,IAAI,CAACY;MACZ,CAAC;IACH,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAG;IAClB,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACY,GAAG;IAClB;IAEA,IAAIqB,QAAQ,EAAEtB,GAAG,EAAEC,GAAG;IAEtB,IAAI,OAAOgB,IAAI,KAAK,UAAU,EAAE;MAC9BK,QAAQ,GAAGpC,SAAS,CAClB+B,IAAI,EACJ7B,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KACF,CAAC;MAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBb,sBAAsB,CAACnB,IAAI,EAAEgC,QAAQ,CAAC;QAEtC,IAAIhC,IAAI,KAAK,CAAC,EAAc;UAC1BsB,IAAI,GAAGU,QAAQ;QACjB,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAiB;UACpCsB,IAAI,GAAGU,QAAQ,CAACV,IAAI;UACpBZ,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIP,KAAK,CAACO,GAAG;UAC/BC,GAAG,GAAGqB,QAAQ,CAACrB,GAAG,IAAIR,KAAK,CAACQ,GAAG;UAE/BR,KAAK,GAAG;YAAEO,GAAG,EAAEA,GAAG;YAAEC,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLR,KAAK,GAAG6B,QAAQ;QAClB;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAIpC,GAAG,GAAG8B,IAAI,CAACM,CAAC,CAAC;QAEjBD,QAAQ,GAAGpC,SAAS,CAClBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJV,YAAY,EACZW,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KACF,CAAC;QAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;UACvBb,sBAAsB,CAACnB,IAAI,EAAEgC,QAAQ,CAAC;UACtC,IAAIG,OAAO;UAEX,IAAInC,IAAI,KAAK,CAAC,EAAc;YAC1BmC,OAAO,GAAGH,QAAQ;UACpB,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAiB;YACpCmC,OAAO,GAAGH,QAAQ,CAACV,IAAI;YACvBZ,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIP,KAAK,CAACO,GAAG;YAC/BC,GAAG,GAAGqB,QAAQ,CAACrB,GAAG,IAAIR,KAAK,CAACQ,GAAG;YAE/BR,KAAK,GAAG;cAAEO,GAAG,EAAEA,GAAG;cAAEC,GAAG,EAAEA;YAAI,CAAC;UAChC,CAAC,MAAM;YACLR,KAAK,GAAG6B,QAAQ;UAClB;UAEA,IAAIG,OAAO,KAAK,KAAK,CAAC,EAAE;YACtB,IAAIb,IAAI,KAAK,KAAK,CAAC,EAAE;cACnBA,IAAI,GAAGa,OAAO;YAChB,CAAC,MAAM,IAAI,OAAOb,IAAI,KAAK,UAAU,EAAE;cACrCA,IAAI,GAAG,CAACA,IAAI,EAAEa,OAAO,CAAC;YACxB,CAAC,MAAM;cACLb,IAAI,CAAC3B,IAAI,CAACwC,OAAO,CAAC;YACpB;UACF;QACF;MACF;IACF;IAEA,IAAInC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvD,IAAIsB,IAAI,KAAK,KAAK,CAAC,EAAE;QAEnBA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,OAAOA,IAAI;QACb,CAAC;MACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QACrC,IAAIe,eAAe,GAAGf,IAAI;QAE1BA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,IAAInB,KAAK,GAAGmB,IAAI;UAEhB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,eAAe,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;YAC/C9B,KAAK,GAAGkC,eAAe,CAACJ,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,EAAEjC,KAAK,CAAC;UAClD;UAEA,OAAOA,KAAK;QACd,CAAC;MACH,CAAC,MAAM;QACL,IAAImC,mBAAmB,GAAGhB,IAAI;QAE9BA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,OAAOgB,mBAAmB,CAAC1B,IAAI,CAACwB,QAAQ,EAAEd,IAAI,CAAC;QACjD,CAAC;MACH;MAEAE,GAAG,CAAC7B,IAAI,CAAC2B,IAAI,CAAC;IAChB;IAEA,IAAItB,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;QAC7BD,IAAI,CAACW,GAAG,GAAGP,KAAK,CAACO,GAAG;QACpBX,IAAI,CAACY,GAAG,GAAGR,KAAK,CAACQ,GAAG;MACtB,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;MACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACW,GAAG,GAAGP,KAAK;MAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACY,GAAG,GAAGR,KAAK;MAClB;MAEA,IAAID,SAAS,EAAE;QACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;UAC7BwB,GAAG,CAAC7B,IAAI,CAAC,UAAUyC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOpC,KAAK,CAACO,GAAG,CAACE,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;UACFf,GAAG,CAAC7B,IAAI,CAAC,UAAUyC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOpC,KAAK,CAACQ,GAAG,CAACC,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIvC,IAAI,KAAK,CAAC,EAAe;UAClCwB,GAAG,CAAC7B,IAAI,CAACQ,KAAK,CAAC;QACjB,CAAC,MAAM;UACLqB,GAAG,CAAC7B,IAAI,CAAC,UAAUyC,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOpC,KAAK,CAACS,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACnC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLT,MAAM,CAACU,cAAc,CAACf,IAAI,EAAE3B,IAAI,EAAEC,IAAI,CAAC;MACzC;IACF;EACF;EAEA,SAAS0C,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACxC,IAAInB,GAAG,GAAG,EAAE;IACZ,IAAIoB,iBAAiB;IACrB,IAAIC,kBAAkB;IAEtB,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IAEvC,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIP,OAAO,GAAGiB,QAAQ,CAACV,CAAC,CAAC;MAGzB,IAAI,CAACgB,KAAK,CAACC,OAAO,CAACxB,OAAO,CAAC,EAAE;MAE7B,IAAI1B,IAAI,GAAG0B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI5B,IAAI,GAAG4B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIxB,SAAS,GAAGwB,OAAO,CAACQ,MAAM,GAAG,CAAC;MAElC,IAAIjC,QAAQ,GAAGD,IAAI,IAAI,CAAC;MACxB,IAAIyB,IAAI;MACR,IAAIpC,YAAY;MAEhB,IAAIY,QAAQ,EAAE;QACZwB,IAAI,GAAGiB,KAAK;QACZ1C,IAAI,GAAGA,IAAI,GAAG,CAAC;QAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;UAC1B6C,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;UAC7CxD,YAAY,GAAGwD,kBAAkB;QACnC;MACF,CAAC,MAAM;QACLpB,IAAI,GAAGiB,KAAK,CAACS,SAAS;QAEtB,IAAInD,IAAI,KAAK,CAAC,EAAc;UAC1B4C,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;UAC3CvD,YAAY,GAAGuD,iBAAiB;QAClC;MACF;MAEA,IAAI5C,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;QACxC,IAAIkD,iBAAiB,GAAGnD,QAAQ,GAC5B+C,uBAAuB,GACvBF,sBAAsB;QAE1B,IAAIO,YAAY,GAAGD,iBAAiB,CAAC1C,GAAG,CAACZ,IAAI,CAAC,IAAI,CAAC;QAEnD,IACEuD,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiBrD,IAAI,KAAK,CAAE,IAC9CqD,YAAY,KAAK,CAAC,IAAiBrD,IAAI,KAAK,CAAE,EAC/C;UACA,MAAM,IAAIe,KAAK,CACb,uMAAuM,GACrMjB,IACJ,CAAC;QACH,CAAC,MAAM,IAAI,CAACuD,YAAY,IAAIrD,IAAI,GAAG,CAAC,EAAe;UACjDoD,iBAAiB,CAACzC,GAAG,CAACb,IAAI,EAAEE,IAAI,CAAC;QACnC,CAAC,MAAM;UACLoD,iBAAiB,CAACzC,GAAG,CAACb,IAAI,EAAE,IAAI,CAAC;QACnC;MACF;MAEAyB,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP5B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTb,YACF,CAAC;IACH;IAEAiE,gBAAgB,CAAC9B,GAAG,EAAEoB,iBAAiB,CAAC;IACxCU,gBAAgB,CAAC9B,GAAG,EAAEqB,kBAAkB,CAAC;IACzC,OAAOrB,GAAG;EACZ;EAEA,SAAS8B,gBAAgBA,CAAC9B,GAAG,EAAEnC,YAAY,EAAE;IAC3C,IAAIA,YAAY,EAAE;MAChBmC,GAAG,CAAC7B,IAAI,CAAC,UAAUyC,QAAQ,EAAE;QAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,YAAY,CAAC6C,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C5C,YAAY,CAAC4C,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,CAAC;QAChC;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF;EAEA,SAASmB,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;IAC9C,IAAIA,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI7C,YAAY,GAAG,EAAE;MACrB,IAAIqE,QAAQ,GAAGF,WAAW;MAC1B,IAAI1D,IAAI,GAAG0D,WAAW,CAAC1D,IAAI;MAE3B,KAAK,IAAImC,CAAC,GAAGwB,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9C,IAAI3C,oBAAoB,GAAG;UAAEmB,CAAC,EAAE;QAAM,CAAC;QAEvC,IAAI;UACF,IAAIkD,YAAY,GAAGF,SAAS,CAACxB,CAAC,CAAC,CAACyB,QAAQ,EAAE;YACxC1D,IAAI,EAAE,OAAO;YACbF,IAAI,EAAEA,IAAI;YACVP,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBACF;UACF,CAAC,CAAC;QACJ,CAAC,SAAS;UACRA,oBAAoB,CAACmB,CAAC,GAAG,IAAI;QAC/B;QAEA,IAAIkD,YAAY,KAAKtC,SAAS,EAAE;UAC9BF,sBAAsB,CAAC,EAAE,EAAcwC,YAAY,CAAC;UACpDD,QAAQ,GAAGC,YAAY;QACzB;MACF;MAEA,OAAO,CACLD,QAAQ,EACR,YAAY;QACV,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,YAAY,CAAC6C,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C5C,YAAY,CAAC4C,CAAC,CAAC,CAACrB,IAAI,CAAC8C,QAAQ,CAAC;QAChC;MACF,CAAC,CACF;IACH;EAGF;EAoJA,OAAO,SAASE,cAAcA,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;IACjE,OAAO;MACLK,CAAC,EAAErB,eAAe,CAACe,WAAW,EAAEK,UAAU,CAAC;MAE3C,IAAIE,CAACA,CAAA,EAAG;QACN,OAAOR,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;MAC/C;IACF,CAAC;EACH,CAAC;AACH;AAEe,SAASG,cAAcA,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;EACzE,OAAO,CAAAO,OAAA,CAAAC,OAAA,GAACL,cAAc,GAAGzE,qBAAqB,CAAC,CAAC,EAC9CqE,WAAW,EACXK,UAAU,EACVJ,SACF,CAAC;AACH", "ignoreList": []}