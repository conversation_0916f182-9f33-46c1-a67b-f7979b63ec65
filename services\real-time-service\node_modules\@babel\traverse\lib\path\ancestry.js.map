{"version": 3, "names": ["_t", "require", "VISITOR_KEYS", "findParent", "callback", "path", "parentPath", "find", "getFunctionParent", "p", "isFunction", "getStatementParent", "Array", "isArray", "container", "isStatement", "isProgram", "isFile", "Error", "getEarliestCommonAncestorFrom", "paths", "getDeepestCommonAncestorFrom", "deepest", "i", "ancestries", "earliest", "keys", "type", "ancestry", "<PERSON><PERSON><PERSON>", "key", "earliestKeyIndex", "indexOf", "parent<PERSON><PERSON>", "currentKeyIndex", "filter", "length", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "lastCommonIndex", "<PERSON><PERSON><PERSON><PERSON>", "map", "unshift", "first", "depthLoop", "shouldMatch", "getAncestry", "push", "isAncestor", "maybeDescendant", "isDescendant", "<PERSON><PERSON><PERSON><PERSON>", "parent", "inType", "candidateTypes", "includes", "node"], "sources": ["../../src/path/ancestry.ts"], "sourcesContent": ["// This file contains that retrieve or validate anything related to the current paths ancestry.\n\nimport { VISITOR_KEYS } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type NodePath from \"./index.ts\";\n\n/**\n * Starting at the parent path of the current `NodePath` and going up the\n * tree, return the first `NodePath` that causes the provided `callback`\n * to return a truthy value, or `null` if the `callback` never returns a\n * truthy value.\n */\n\nexport function findParent(\n  this: NodePath,\n  callback: (path: NodePath) => boolean,\n): NodePath | null {\n  let path = this;\n  while ((path = path.parentPath)) {\n    if (callback(path)) return path;\n  }\n  return null;\n}\n\n/**\n * Starting at current `NodePath` and going up the tree, return the first\n * `NodePath` that causes the provided `callback` to return a truthy value,\n * or `null` if the `callback` never returns a truthy value.\n */\n\nexport function find(\n  this: NodePath,\n  callback: (path: NodePath) => boolean,\n): NodePath | null {\n  let path = this;\n  do {\n    if (callback(path)) return path;\n  } while ((path = path.parentPath));\n  return null;\n}\n\n/**\n * Get the parent function of the current path.\n */\n\nexport function getFunctionParent(this: NodePath): NodePath<t.Function> | null {\n  return this.findParent(p => p.isFunction()) as NodePath<t.Function> | null;\n}\n\n/**\n * Walk up the tree until we hit a parent node path in a list.\n */\n\nexport function getStatementParent(this: NodePath): NodePath<t.Statement> {\n  let path = this;\n\n  do {\n    if (\n      !path.parentPath ||\n      (Array.isArray(path.container) && path.isStatement())\n    ) {\n      break;\n    } else {\n      path = path.parentPath;\n    }\n  } while (path);\n\n  if (path && (path.isProgram() || path.isFile())) {\n    throw new Error(\n      \"File/Program node, we can't possibly find a statement parent to this\",\n    );\n  }\n\n  return path as NodePath<t.Statement>;\n}\n\n/**\n * Get the deepest common ancestor and then from it, get the earliest relationship path\n * to that ancestor.\n *\n * Earliest is defined as being \"before\" all the other nodes in terms of list container\n * position and visiting key.\n */\n\nexport function getEarliestCommonAncestorFrom(\n  this: NodePath,\n  paths: Array<NodePath>,\n): NodePath {\n  return this.getDeepestCommonAncestorFrom(\n    paths,\n    function (deepest, i, ancestries) {\n      let earliest;\n      const keys = VISITOR_KEYS[deepest.type];\n\n      for (const ancestry of ancestries) {\n        const path = ancestry[i + 1];\n\n        // first path\n        if (!earliest) {\n          earliest = path;\n          continue;\n        }\n\n        // handle containers\n        if (path.listKey && earliest.listKey === path.listKey) {\n          // we're in the same container so check if we're earlier\n          if (path.key < earliest.key) {\n            earliest = path;\n            continue;\n          }\n        }\n\n        // handle keys\n        const earliestKeyIndex = keys.indexOf(earliest.parentKey);\n        const currentKeyIndex = keys.indexOf(path.parentKey);\n        if (earliestKeyIndex > currentKeyIndex) {\n          // key appears before so it's earlier\n          earliest = path;\n        }\n      }\n\n      return earliest;\n    },\n  );\n}\n\n/**\n * Get the earliest path in the tree where the provided `paths` intersect.\n *\n * TODO: Possible optimisation target.\n */\n\nexport function getDeepestCommonAncestorFrom(\n  this: NodePath,\n  paths: Array<NodePath>,\n  filter?: (deepest: NodePath, i: number, ancestries: NodePath[][]) => NodePath,\n): NodePath {\n  if (!paths.length) {\n    return this;\n  }\n\n  if (paths.length === 1) {\n    return paths[0];\n  }\n\n  // minimum depth of the tree so we know the highest node\n  let minDepth = Infinity;\n\n  // last common ancestor\n  let lastCommonIndex, lastCommon;\n\n  // get the ancestors of the path, breaking when the parent exceeds ourselves\n  const ancestries = paths.map(path => {\n    const ancestry: NodePath[] = [];\n\n    do {\n      ancestry.unshift(path);\n    } while ((path = path.parentPath) && path !== this);\n\n    // save min depth to avoid going too far in\n    if (ancestry.length < minDepth) {\n      minDepth = ancestry.length;\n    }\n\n    return ancestry;\n  });\n\n  // get the first ancestry so we have a seed to assess all other ancestries with\n  const first = ancestries[0];\n\n  // check ancestor equality\n  depthLoop: for (let i = 0; i < minDepth; i++) {\n    const shouldMatch = first[i];\n\n    for (const ancestry of ancestries) {\n      if (ancestry[i] !== shouldMatch) {\n        // we've hit a snag\n        break depthLoop;\n      }\n    }\n\n    // next iteration may break so store these so they can be returned\n    lastCommonIndex = i;\n    lastCommon = shouldMatch;\n  }\n\n  if (lastCommon) {\n    if (filter) {\n      return filter(lastCommon, lastCommonIndex, ancestries);\n    } else {\n      return lastCommon;\n    }\n  } else {\n    throw new Error(\"Couldn't find intersection\");\n  }\n}\n\n/**\n * Build an array of node paths containing the entire ancestry of the current node path.\n *\n * NOTE: The current node path is included in this.\n */\n\nexport function getAncestry(this: NodePath): Array<NodePath> {\n  let path = this;\n  const paths = [];\n  do {\n    paths.push(path);\n  } while ((path = path.parentPath));\n  return paths;\n}\n\n/**\n * A helper to find if `this` path is an ancestor of @param maybeDescendant\n */\nexport function isAncestor(this: NodePath, maybeDescendant: NodePath): boolean {\n  return maybeDescendant.isDescendant(this);\n}\n\n/**\n * A helper to find if `this` path is a descendant of @param maybeAncestor\n */\nexport function isDescendant(this: NodePath, maybeAncestor: NodePath): boolean {\n  return !!this.findParent(parent => parent === maybeAncestor);\n}\n\nexport function inType(this: NodePath, ...candidateTypes: string[]): boolean {\n  let path = this;\n  while (path) {\n    if (candidateTypes.includes(path.node.type)) return true;\n    path = path.parentPath;\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AAA4C;EAAnCC;AAAY,IAAAF,EAAA;AAWd,SAASG,UAAUA,CAExBC,QAAqC,EACpB;EACjB,IAAIC,IAAI,GAAG,IAAI;EACf,OAAQA,IAAI,GAAGA,IAAI,CAACC,UAAU,EAAG;IAC/B,IAAIF,QAAQ,CAACC,IAAI,CAAC,EAAE,OAAOA,IAAI;EACjC;EACA,OAAO,IAAI;AACb;AAQO,SAASE,IAAIA,CAElBH,QAAqC,EACpB;EACjB,IAAIC,IAAI,GAAG,IAAI;EACf,GAAG;IACD,IAAID,QAAQ,CAACC,IAAI,CAAC,EAAE,OAAOA,IAAI;EACjC,CAAC,QAASA,IAAI,GAAGA,IAAI,CAACC,UAAU;EAChC,OAAO,IAAI;AACb;AAMO,SAASE,iBAAiBA,CAAA,EAA8C;EAC7E,OAAO,IAAI,CAACL,UAAU,CAACM,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;AAC7C;AAMO,SAASC,kBAAkBA,CAAA,EAAwC;EACxE,IAAIN,IAAI,GAAG,IAAI;EAEf,GAAG;IACD,IACE,CAACA,IAAI,CAACC,UAAU,IACfM,KAAK,CAACC,OAAO,CAACR,IAAI,CAACS,SAAS,CAAC,IAAIT,IAAI,CAACU,WAAW,CAAC,CAAE,EACrD;MACA;IACF,CAAC,MAAM;MACLV,IAAI,GAAGA,IAAI,CAACC,UAAU;IACxB;EACF,CAAC,QAAQD,IAAI;EAEb,IAAIA,IAAI,KAAKA,IAAI,CAACW,SAAS,CAAC,CAAC,IAAIX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,EAAE;IAC/C,MAAM,IAAIC,KAAK,CACb,sEACF,CAAC;EACH;EAEA,OAAOb,IAAI;AACb;AAUO,SAASc,6BAA6BA,CAE3CC,KAAsB,EACZ;EACV,OAAO,IAAI,CAACC,4BAA4B,CACtCD,KAAK,EACL,UAAUE,OAAO,EAAEC,CAAC,EAAEC,UAAU,EAAE;IAChC,IAAIC,QAAQ;IACZ,MAAMC,IAAI,GAAGxB,YAAY,CAACoB,OAAO,CAACK,IAAI,CAAC;IAEvC,KAAK,MAAMC,QAAQ,IAAIJ,UAAU,EAAE;MACjC,MAAMnB,IAAI,GAAGuB,QAAQ,CAACL,CAAC,GAAG,CAAC,CAAC;MAG5B,IAAI,CAACE,QAAQ,EAAE;QACbA,QAAQ,GAAGpB,IAAI;QACf;MACF;MAGA,IAAIA,IAAI,CAACwB,OAAO,IAAIJ,QAAQ,CAACI,OAAO,KAAKxB,IAAI,CAACwB,OAAO,EAAE;QAErD,IAAIxB,IAAI,CAACyB,GAAG,GAAGL,QAAQ,CAACK,GAAG,EAAE;UAC3BL,QAAQ,GAAGpB,IAAI;UACf;QACF;MACF;MAGA,MAAM0B,gBAAgB,GAAGL,IAAI,CAACM,OAAO,CAACP,QAAQ,CAACQ,SAAS,CAAC;MACzD,MAAMC,eAAe,GAAGR,IAAI,CAACM,OAAO,CAAC3B,IAAI,CAAC4B,SAAS,CAAC;MACpD,IAAIF,gBAAgB,GAAGG,eAAe,EAAE;QAEtCT,QAAQ,GAAGpB,IAAI;MACjB;IACF;IAEA,OAAOoB,QAAQ;EACjB,CACF,CAAC;AACH;AAQO,SAASJ,4BAA4BA,CAE1CD,KAAsB,EACtBe,MAA6E,EACnE;EACV,IAAI,CAACf,KAAK,CAACgB,MAAM,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,IAAIhB,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOhB,KAAK,CAAC,CAAC,CAAC;EACjB;EAGA,IAAIiB,QAAQ,GAAGC,QAAQ;EAGvB,IAAIC,eAAe,EAAEC,UAAU;EAG/B,MAAMhB,UAAU,GAAGJ,KAAK,CAACqB,GAAG,CAACpC,IAAI,IAAI;IACnC,MAAMuB,QAAoB,GAAG,EAAE;IAE/B,GAAG;MACDA,QAAQ,CAACc,OAAO,CAACrC,IAAI,CAAC;IACxB,CAAC,QAAQ,CAACA,IAAI,GAAGA,IAAI,CAACC,UAAU,KAAKD,IAAI,KAAK,IAAI;IAGlD,IAAIuB,QAAQ,CAACQ,MAAM,GAAGC,QAAQ,EAAE;MAC9BA,QAAQ,GAAGT,QAAQ,CAACQ,MAAM;IAC5B;IAEA,OAAOR,QAAQ;EACjB,CAAC,CAAC;EAGF,MAAMe,KAAK,GAAGnB,UAAU,CAAC,CAAC,CAAC;EAG3BoB,SAAS,EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,QAAQ,EAAEd,CAAC,EAAE,EAAE;IAC5C,MAAMsB,WAAW,GAAGF,KAAK,CAACpB,CAAC,CAAC;IAE5B,KAAK,MAAMK,QAAQ,IAAIJ,UAAU,EAAE;MACjC,IAAII,QAAQ,CAACL,CAAC,CAAC,KAAKsB,WAAW,EAAE;QAE/B,MAAMD,SAAS;MACjB;IACF;IAGAL,eAAe,GAAGhB,CAAC;IACnBiB,UAAU,GAAGK,WAAW;EAC1B;EAEA,IAAIL,UAAU,EAAE;IACd,IAAIL,MAAM,EAAE;MACV,OAAOA,MAAM,CAACK,UAAU,EAAED,eAAe,EAAEf,UAAU,CAAC;IACxD,CAAC,MAAM;MACL,OAAOgB,UAAU;IACnB;EACF,CAAC,MAAM;IACL,MAAM,IAAItB,KAAK,CAAC,4BAA4B,CAAC;EAC/C;AACF;AAQO,SAAS4B,WAAWA,CAAA,EAAkC;EAC3D,IAAIzC,IAAI,GAAG,IAAI;EACf,MAAMe,KAAK,GAAG,EAAE;EAChB,GAAG;IACDA,KAAK,CAAC2B,IAAI,CAAC1C,IAAI,CAAC;EAClB,CAAC,QAASA,IAAI,GAAGA,IAAI,CAACC,UAAU;EAChC,OAAOc,KAAK;AACd;AAKO,SAAS4B,UAAUA,CAAiBC,eAAyB,EAAW;EAC7E,OAAOA,eAAe,CAACC,YAAY,CAAC,IAAI,CAAC;AAC3C;AAKO,SAASA,YAAYA,CAAiBC,aAAuB,EAAW;EAC7E,OAAO,CAAC,CAAC,IAAI,CAAChD,UAAU,CAACiD,MAAM,IAAIA,MAAM,KAAKD,aAAa,CAAC;AAC9D;AAEO,SAASE,MAAMA,CAAiB,GAAGC,cAAwB,EAAW;EAC3E,IAAIjD,IAAI,GAAG,IAAI;EACf,OAAOA,IAAI,EAAE;IACX,IAAIiD,cAAc,CAACC,QAAQ,CAAClD,IAAI,CAACmD,IAAI,CAAC7B,IAAI,CAAC,EAAE,OAAO,IAAI;IACxDtB,IAAI,GAAGA,IAAI,CAACC,UAAU;EACxB;EAEA,OAAO,KAAK;AACd", "ignoreList": []}