const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const winston = require('winston');
const axios = require('axios');

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 8035;

// Configure Winston logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/realtime.log' })
    ]
});

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'kontour-realtime',
        timestamp: new Date().toISOString(),
        connections: io.engine.clientsCount,
        uptime: process.uptime()
    });
});

// Real-time data storage
let marketData = new Map();
let walletData = new Map();
let agentStatus = new Map();
let workflowStatus = new Map();
let swapActivity = [];

// WebSocket connection handling
io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}`);
    
    // Send initial data
    socket.emit('market_data', generateMarketData());
    socket.emit('system_status', getSystemStatus());
    socket.emit('wallet_data', generateWalletData());
    socket.emit('agent_status', generateAgentStatus());
    socket.emit('workflow_updates', generateWorkflowUpdates());
    
    // Handle client subscriptions
    socket.on('subscribe', (data) => {
        const { channel, symbol } = data;
        socket.join(`${channel}:${symbol || 'all'}`);
        logger.info(`Client ${socket.id} subscribed to ${channel}:${symbol || 'all'}`);
        
        // Send channel-specific initial data
        switch(channel) {
            case 'market':
                socket.emit('market_update', generateMarketUpdate(symbol));
                break;
            case 'wallet':
                socket.emit('wallet_update', generateWalletUpdate());
                break;
            case 'agents':
                socket.emit('agent_update', generateAgentUpdate());
                break;
            case 'workflows':
                socket.emit('workflow_update', generateWorkflowUpdate());
                break;
            case 'swaps':
                socket.emit('swap_update', generateSwapUpdate());
                break;
        }
    });
    
    socket.on('unsubscribe', (data) => {
        const { channel, symbol } = data;
        socket.leave(`${channel}:${symbol || 'all'}`);
        logger.info(`Client ${socket.id} unsubscribed from ${channel}:${symbol || 'all'}`);
    });
    
    // Handle real-time actions
    socket.on('execute_swap', async (data) => {
        try {
            const swapResult = await processSwap(data);
            socket.emit('swap_result', swapResult);
            io.to('swaps:all').emit('swap_executed', swapResult);
            
            // Update wallet data
            const walletUpdate = await updateWalletAfterSwap(swapResult);
            io.to('wallet:all').emit('wallet_update', walletUpdate);
        } catch (error) {
            socket.emit('swap_error', { error: error.message });
        }
    });
    
    socket.on('create_workflow', async (data) => {
        try {
            const workflow = await createWorkflow(data);
            socket.emit('workflow_created', workflow);
            io.to('workflows:all').emit('new_workflow', workflow);
        } catch (error) {
            socket.emit('workflow_error', { error: error.message });
        }
    });
    
    socket.on('agent_command', async (data) => {
        try {
            const result = await executeAgentCommand(data);
            socket.emit('agent_response', result);
            io.to('agents:all').emit('agent_activity', result);
        } catch (error) {
            socket.emit('agent_error', { error: error.message });
        }
    });
    
    socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
    });
});

// Data generation functions
function generateMarketData() {
    const tokens = ['KONTOUR', 'ETH', 'BTC', 'USDC', 'USDT', 'SOL', 'ADA', 'DOT'];
    const data = {};
    
    tokens.forEach(token => {
        const basePrice = getBasePrice(token);
        const change = (Math.random() - 0.5) * 10;
        data[token] = {
            symbol: token,
            price: (basePrice * (1 + change / 100)).toFixed(token === 'BTC' ? 2 : 4),
            change24h: change.toFixed(2),
            volume24h: (Math.random() * 1000000).toFixed(0),
            marketCap: (Math.random() * 10000000000).toFixed(0),
            timestamp: new Date().toISOString()
        };
    });
    
    return data;
}

function generateWalletData() {
    return {
        totalValue: (24567.89 + (Math.random() - 0.5) * 1000).toFixed(2),
        change24h: ((Math.random() - 0.5) * 10).toFixed(2),
        balances: {
            KONTOUR: (1000 + Math.random() * 100).toFixed(4),
            ETH: (2.5 + Math.random() * 0.5).toFixed(4),
            BTC: (0.1 + Math.random() * 0.05).toFixed(6),
            USDC: (5000 + Math.random() * 500).toFixed(2),
            USDT: (3000 + Math.random() * 300).toFixed(2),
            SOL: (50 + Math.random() * 10).toFixed(4)
        },
        lastUpdate: new Date().toISOString()
    };
}

function generateAgentStatus() {
    const agents = [
        { id: 'coordinator-1', type: 'coordinator', status: 'active' },
        { id: 'specialist-1', type: 'specialist', status: 'active' },
        { id: 'validator-1', type: 'validator', status: 'idle' },
        { id: 'optimizer-1', type: 'optimizer', status: 'training' },
        { id: 'learner-1', type: 'learner', status: 'active' }
    ];
    
    return agents.map(agent => ({
        ...agent,
        performance: (0.8 + Math.random() * 0.2).toFixed(3),
        tasksCompleted: Math.floor(Math.random() * 200) + 50,
        lastActive: new Date().toISOString()
    }));
}

function generateWorkflowUpdates() {
    return {
        activeWorkflows: Math.floor(Math.random() * 10) + 5,
        completedToday: Math.floor(Math.random() * 50) + 20,
        averageExecutionTime: (Math.random() * 30 + 10).toFixed(1) + 's',
        successRate: (95 + Math.random() * 5).toFixed(1) + '%',
        lastUpdate: new Date().toISOString()
    };
}

function getBasePrice(token) {
    const prices = {
        'KONTOUR': 75.42,
        'ETH': 2456.78,
        'BTC': 43567.89,
        'USDC': 1.00,
        'USDT': 0.999,
        'SOL': 98.76,
        'ADA': 0.456,
        'DOT': 6.78
    };
    return prices[token] || 1;
}

function getSystemStatus() {
    return {
        services: {
            apiGateway: 'healthy',
            walletService: 'healthy',
            agenticAI: 'healthy',
            neuralNetwork: 'healthy',
            bigData: 'healthy'
        },
        performance: {
            cpu: Math.random() * 100,
            memory: Math.random() * 100,
            network: Math.random() * 100
        },
        timestamp: new Date().toISOString()
    };
}

// Action handlers
async function processSwap(swapData) {
    // Simulate swap processing
    const { fromToken, toToken, amount, walletId } = swapData;
    
    const swapId = `swap_${Date.now()}`;
    const result = {
        swapId,
        fromToken,
        toToken,
        fromAmount: amount,
        toAmount: (parseFloat(amount) * 0.97).toFixed(6), // 3% slippage simulation
        status: 'pending',
        txHash: '0x' + Math.random().toString(16).substr(2, 64),
        timestamp: new Date().toISOString()
    };
    
    // Add to swap activity
    swapActivity.unshift(result);
    if (swapActivity.length > 100) swapActivity.pop();
    
    // Simulate processing delay
    setTimeout(() => {
        result.status = 'completed';
        io.to('swaps:all').emit('swap_completed', result);
    }, 5000);
    
    return result;
}

async function createWorkflow(workflowData) {
    const workflowId = `workflow_${Date.now()}`;
    const workflow = {
        id: workflowId,
        name: workflowData.name || 'New Workflow',
        nodes: workflowData.nodes || [],
        status: 'active',
        createdAt: new Date().toISOString()
    };
    
    workflowStatus.set(workflowId, workflow);
    return workflow;
}

async function executeAgentCommand(commandData) {
    const { agentId, command, parameters } = commandData;
    
    const result = {
        agentId,
        command,
        status: 'executed',
        result: `Command ${command} executed successfully`,
        timestamp: new Date().toISOString()
    };
    
    return result;
}

async function updateWalletAfterSwap(swapResult) {
    // Simulate wallet balance update after swap
    return {
        swapId: swapResult.swapId,
        updatedBalances: generateWalletData().balances,
        timestamp: new Date().toISOString()
    };
}

// Periodic data updates
setInterval(() => {
    io.emit('market_update', generateMarketData());
}, 5000);

setInterval(() => {
    io.to('wallet:all').emit('wallet_update', generateWalletData());
}, 10000);

setInterval(() => {
    io.to('agents:all').emit('agent_update', generateAgentStatus());
}, 15000);

setInterval(() => {
    io.to('workflows:all').emit('workflow_update', generateWorkflowUpdates());
}, 20000);

// API endpoints
app.get('/api/streams/active', (req, res) => {
    res.json({
        success: true,
        streams: {
            market: Array.from(io.sockets.adapter.rooms.get('market:all') || []).length,
            wallet: Array.from(io.sockets.adapter.rooms.get('wallet:all') || []).length,
            agents: Array.from(io.sockets.adapter.rooms.get('agents:all') || []).length,
            workflows: Array.from(io.sockets.adapter.rooms.get('workflows:all') || []).length
        },
        totalConnections: io.engine.clientsCount
    });
});

app.get('/api/market/data', (req, res) => {
    res.json({
        success: true,
        data: generateMarketData()
    });
});

// Start server
server.listen(PORT, () => {
    logger.info(`Real-time service running on port ${PORT}`);
    console.log(`⚡ Real-time service running on port ${PORT}`);
    console.log(`🔄 WebSocket server active`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

module.exports = { app, io };
