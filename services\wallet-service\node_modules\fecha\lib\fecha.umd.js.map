{"version": 3, "file": "fecha.umd.js", "sources": ["../src/fecha.ts"], "sourcesContent": ["const token = /d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\\1?|[aA]|\"[^\"]*\"|'[^']*'/g;\nconst twoDigitsOptional = \"\\\\d\\\\d?\";\nconst twoDigits = \"\\\\d\\\\d\";\nconst threeDigits = \"\\\\d{3}\";\nconst fourDigits = \"\\\\d{4}\";\nconst word = \"[^\\\\s]+\";\nconst literal = /\\[([^]*?)\\]/gm;\n\ntype DateInfo = {\n  year: number;\n  month: number;\n  day: number;\n  hour: number;\n  minute: number;\n  second: number;\n  millisecond: number;\n  isPm: number | null;\n  timezoneOffset: number | null;\n};\n\nexport type I18nSettings = {\n  amPm: [string, string];\n  dayNames: Days;\n  dayNamesShort: Days;\n  monthNames: Months;\n  monthNamesShort: Months;\n  DoFn(dayOfMonth: number): string;\n};\n\nexport type I18nSettingsOptional = Partial<I18nSettings>;\n\nexport type Days = [string, string, string, string, string, string, string];\nexport type Months = [\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string\n];\n\nfunction shorten<T extends string[]>(arr: T, sLen: number): string[] {\n  const newArr: string[] = [];\n  for (let i = 0, len = arr.length; i < len; i++) {\n    newArr.push(arr[i].substr(0, sLen));\n  }\n  return newArr;\n}\n\nconst monthUpdate = (\n  arrName: \"monthNames\" | \"monthNamesShort\" | \"dayNames\" | \"dayNamesShort\"\n) => (v: string, i18n: I18nSettings): number | null => {\n  const lowerCaseArr = i18n[arrName].map(v => v.toLowerCase());\n  const index = lowerCaseArr.indexOf(v.toLowerCase());\n  if (index > -1) {\n    return index;\n  }\n  return null;\n};\n\nexport function assign<A>(a: A): A;\nexport function assign<A, B>(a: A, b: B): A & B;\nexport function assign<A, B, C>(a: A, b: B, c: C): A & B & C;\nexport function assign<A, B, C, D>(a: A, b: B, c: C, d: D): A & B & C & D;\nexport function assign(origObj: any, ...args: any[]): any {\n  for (const obj of args) {\n    for (const key in obj) {\n      // @ts-ignore ex\n      origObj[key] = obj[key];\n    }\n  }\n  return origObj;\n}\n\nconst dayNames: Days = [\n  \"Sunday\",\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\"\n];\nconst monthNames: Months = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\"\n];\n\nconst monthNamesShort: Months = shorten(monthNames, 3) as Months;\nconst dayNamesShort: Days = shorten(dayNames, 3) as Days;\n\nconst defaultI18n: I18nSettings = {\n  dayNamesShort,\n  dayNames,\n  monthNamesShort,\n  monthNames,\n  amPm: [\"am\", \"pm\"],\n  DoFn(dayOfMonth: number) {\n    return (\n      dayOfMonth +\n      [\"th\", \"st\", \"nd\", \"rd\"][\n        dayOfMonth % 10 > 3\n          ? 0\n          : ((dayOfMonth - (dayOfMonth % 10) !== 10 ? 1 : 0) * dayOfMonth) % 10\n      ]\n    );\n  }\n};\nlet globalI18n = assign({}, defaultI18n);\nconst setGlobalDateI18n = (i18n: I18nSettingsOptional): I18nSettings =>\n  (globalI18n = assign(globalI18n, i18n));\n\nconst regexEscape = (str: string): string =>\n  str.replace(/[|\\\\{()[^$+*?.-]/g, \"\\\\$&\");\n\nconst pad = (val: string | number, len = 2): string => {\n  val = String(val);\n  while (val.length < len) {\n    val = \"0\" + val;\n  }\n  return val;\n};\n\nconst formatFlags: Record<\n  string,\n  (dateObj: Date, i18n: I18nSettings) => string\n> = {\n  D: (dateObj: Date): string => String(dateObj.getDate()),\n  DD: (dateObj: Date): string => pad(dateObj.getDate()),\n  Do: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.DoFn(dateObj.getDate()),\n  d: (dateObj: Date): string => String(dateObj.getDay()),\n  dd: (dateObj: Date): string => pad(dateObj.getDay()),\n  ddd: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.dayNamesShort[dateObj.getDay()],\n  dddd: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.dayNames[dateObj.getDay()],\n  M: (dateObj: Date): string => String(dateObj.getMonth() + 1),\n  MM: (dateObj: Date): string => pad(dateObj.getMonth() + 1),\n  MMM: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.monthNamesShort[dateObj.getMonth()],\n  MMMM: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.monthNames[dateObj.getMonth()],\n  YY: (dateObj: Date): string =>\n    pad(String(dateObj.getFullYear()), 4).substr(2),\n  YYYY: (dateObj: Date): string => pad(dateObj.getFullYear(), 4),\n  h: (dateObj: Date): string => String(dateObj.getHours() % 12 || 12),\n  hh: (dateObj: Date): string => pad(dateObj.getHours() % 12 || 12),\n  H: (dateObj: Date): string => String(dateObj.getHours()),\n  HH: (dateObj: Date): string => pad(dateObj.getHours()),\n  m: (dateObj: Date): string => String(dateObj.getMinutes()),\n  mm: (dateObj: Date): string => pad(dateObj.getMinutes()),\n  s: (dateObj: Date): string => String(dateObj.getSeconds()),\n  ss: (dateObj: Date): string => pad(dateObj.getSeconds()),\n  S: (dateObj: Date): string =>\n    String(Math.round(dateObj.getMilliseconds() / 100)),\n  SS: (dateObj: Date): string =>\n    pad(Math.round(dateObj.getMilliseconds() / 10), 2),\n  SSS: (dateObj: Date): string => pad(dateObj.getMilliseconds(), 3),\n  a: (dateObj: Date, i18n: I18nSettings): string =>\n    dateObj.getHours() < 12 ? i18n.amPm[0] : i18n.amPm[1],\n  A: (dateObj: Date, i18n: I18nSettings): string =>\n    dateObj.getHours() < 12\n      ? i18n.amPm[0].toUpperCase()\n      : i18n.amPm[1].toUpperCase(),\n  ZZ(dateObj: Date): string {\n    const offset = dateObj.getTimezoneOffset();\n    return (\n      (offset > 0 ? \"-\" : \"+\") +\n      pad(Math.floor(Math.abs(offset) / 60) * 100 + (Math.abs(offset) % 60), 4)\n    );\n  },\n  Z(dateObj: Date): string {\n    const offset = dateObj.getTimezoneOffset();\n    return (\n      (offset > 0 ? \"-\" : \"+\") +\n      pad(Math.floor(Math.abs(offset) / 60), 2) +\n      \":\" +\n      pad(Math.abs(offset) % 60, 2)\n    );\n  }\n};\n\ntype ParseInfo = [\n  keyof DateInfo,\n  string,\n  ((v: string, i18n: I18nSettings) => number | null)?,\n  string?\n];\nconst monthParse = (v: string): number => +v - 1;\nconst emptyDigits: ParseInfo = [null, twoDigitsOptional];\nconst emptyWord: ParseInfo = [null, word];\nconst amPm: ParseInfo = [\n  \"isPm\",\n  word,\n  (v: string, i18n: I18nSettings): number | null => {\n    const val = v.toLowerCase();\n    if (val === i18n.amPm[0]) {\n      return 0;\n    } else if (val === i18n.amPm[1]) {\n      return 1;\n    }\n    return null;\n  }\n];\nconst timezoneOffset: ParseInfo = [\n  \"timezoneOffset\",\n  \"[^\\\\s]*?[\\\\+\\\\-]\\\\d\\\\d:?\\\\d\\\\d|[^\\\\s]*?Z?\",\n  (v: string): number | null => {\n    const parts = (v + \"\").match(/([+-]|\\d\\d)/gi);\n\n    if (parts) {\n      const minutes = +parts[1] * 60 + parseInt(parts[2], 10);\n      return parts[0] === \"+\" ? minutes : -minutes;\n    }\n\n    return 0;\n  }\n];\nconst parseFlags: Record<string, ParseInfo> = {\n  D: [\"day\", twoDigitsOptional],\n  DD: [\"day\", twoDigits],\n  Do: [\"day\", twoDigitsOptional + word, (v: string): number => parseInt(v, 10)],\n  M: [\"month\", twoDigitsOptional, monthParse],\n  MM: [\"month\", twoDigits, monthParse],\n  YY: [\n    \"year\",\n    twoDigits,\n    (v: string): number => {\n      const now = new Date();\n      const cent = +(\"\" + now.getFullYear()).substr(0, 2);\n      return +(\"\" + (+v > 68 ? cent - 1 : cent) + v);\n    }\n  ],\n  h: [\"hour\", twoDigitsOptional, undefined, \"isPm\"],\n  hh: [\"hour\", twoDigits, undefined, \"isPm\"],\n  H: [\"hour\", twoDigitsOptional],\n  HH: [\"hour\", twoDigits],\n  m: [\"minute\", twoDigitsOptional],\n  mm: [\"minute\", twoDigits],\n  s: [\"second\", twoDigitsOptional],\n  ss: [\"second\", twoDigits],\n  YYYY: [\"year\", fourDigits],\n  S: [\"millisecond\", \"\\\\d\", (v: string): number => +v * 100],\n  SS: [\"millisecond\", twoDigits, (v: string): number => +v * 10],\n  SSS: [\"millisecond\", threeDigits],\n  d: emptyDigits,\n  dd: emptyDigits,\n  ddd: emptyWord,\n  dddd: emptyWord,\n  MMM: [\"month\", word, monthUpdate(\"monthNamesShort\")],\n  MMMM: [\"month\", word, monthUpdate(\"monthNames\")],\n  a: amPm,\n  A: amPm,\n  ZZ: timezoneOffset,\n  Z: timezoneOffset\n};\n\n// Some common format strings\nconst globalMasks: { [key: string]: string } = {\n  default: \"ddd MMM DD YYYY HH:mm:ss\",\n  shortDate: \"M/D/YY\",\n  mediumDate: \"MMM D, YYYY\",\n  longDate: \"MMMM D, YYYY\",\n  fullDate: \"dddd, MMMM D, YYYY\",\n  isoDate: \"YYYY-MM-DD\",\n  isoDateTime: \"YYYY-MM-DDTHH:mm:ssZ\",\n  shortTime: \"HH:mm\",\n  mediumTime: \"HH:mm:ss\",\n  longTime: \"HH:mm:ss.SSS\"\n};\nconst setGlobalDateMasks = (masks: {\n  [key: string]: string;\n}): { [key: string]: string } => assign(globalMasks, masks);\n\n/***\n * Format a date\n * @method format\n * @param {Date|number} dateObj\n * @param {string} mask Format of the date, i.e. 'mm-dd-yy' or 'shortDate'\n * @returns {string} Formatted date string\n */\nconst format = (\n  dateObj: Date,\n  mask: string = globalMasks[\"default\"],\n  i18n: I18nSettingsOptional = {}\n): string => {\n  if (typeof dateObj === \"number\") {\n    dateObj = new Date(dateObj);\n  }\n\n  if (\n    Object.prototype.toString.call(dateObj) !== \"[object Date]\" ||\n    isNaN(dateObj.getTime())\n  ) {\n    throw new Error(\"Invalid Date pass to format\");\n  }\n\n  mask = globalMasks[mask] || mask;\n\n  const literals: string[] = [];\n\n  // Make literals inactive by replacing them with @@@\n  mask = mask.replace(literal, function($0, $1) {\n    literals.push($1);\n    return \"@@@\";\n  });\n\n  const combinedI18nSettings: I18nSettings = assign(\n    assign({}, globalI18n),\n    i18n\n  );\n  // Apply formatting rules\n  mask = mask.replace(token, $0 =>\n    formatFlags[$0](dateObj, combinedI18nSettings)\n  );\n  // Inline literal values back into the formatted value\n  return mask.replace(/@@@/g, () => literals.shift());\n};\n\n/**\n * Parse a date string into a Javascript Date object /\n * @method parse\n * @param {string} dateStr Date string\n * @param {string} format Date parse format\n * @param {i18n} I18nSettingsOptional Full or subset of I18N settings\n * @returns {Date|null} Returns Date object. Returns null what date string is invalid or doesn't match format\n */\nfunction parse(\n  dateStr: string,\n  format: string,\n  i18n: I18nSettingsOptional = {}\n): Date | null {\n  if (typeof format !== \"string\") {\n    throw new Error(\"Invalid format in fecha parse\");\n  }\n\n  // Check to see if the format is actually a mask\n  format = globalMasks[format] || format;\n\n  // Avoid regular expression denial of service, fail early for really long strings\n  // https://www.owasp.org/index.php/Regular_expression_Denial_of_Service_-_ReDoS\n  if (dateStr.length > 1000) {\n    return null;\n  }\n\n  // Default to the beginning of the year.\n  const today = new Date();\n  const dateInfo: DateInfo = {\n    year: today.getFullYear(),\n    month: 0,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n    isPm: null,\n    timezoneOffset: null\n  };\n  const parseInfo: ParseInfo[] = [];\n  const literals: string[] = [];\n\n  // Replace all the literals with @@@. Hopefully a string that won't exist in the format\n  let newFormat = format.replace(literal, ($0, $1) => {\n    literals.push(regexEscape($1));\n    return \"@@@\";\n  });\n  const specifiedFields: { [field: string]: boolean } = {};\n  const requiredFields: { [field: string]: boolean } = {};\n\n  // Change every token that we find into the correct regex\n  newFormat = regexEscape(newFormat).replace(token, $0 => {\n    const info = parseFlags[$0];\n    const [field, regex, , requiredField] = info;\n\n    // Check if the person has specified the same field twice. This will lead to confusing results.\n    if (specifiedFields[field]) {\n      throw new Error(`Invalid format. ${field} specified twice in format`);\n    }\n\n    specifiedFields[field] = true;\n\n    // Check if there are any required fields. For instance, 12 hour time requires AM/PM specified\n    if (requiredField) {\n      requiredFields[requiredField] = true;\n    }\n\n    parseInfo.push(info);\n    return \"(\" + regex + \")\";\n  });\n\n  // Check all the required fields are present\n  Object.keys(requiredFields).forEach(field => {\n    if (!specifiedFields[field]) {\n      throw new Error(\n        `Invalid format. ${field} is required in specified format`\n      );\n    }\n  });\n\n  // Add back all the literals after\n  newFormat = newFormat.replace(/@@@/g, () => literals.shift());\n\n  // Check if the date string matches the format. If it doesn't return null\n  const matches = dateStr.match(new RegExp(newFormat, \"i\"));\n  if (!matches) {\n    return null;\n  }\n\n  const combinedI18nSettings: I18nSettings = assign(\n    assign({}, globalI18n),\n    i18n\n  );\n\n  // For each match, call the parser function for that date part\n  for (let i = 1; i < matches.length; i++) {\n    const [field, , parser] = parseInfo[i - 1];\n    const value = parser\n      ? parser(matches[i], combinedI18nSettings)\n      : +matches[i];\n\n    // If the parser can't make sense of the value, return null\n    if (value == null) {\n      return null;\n    }\n\n    dateInfo[field] = value;\n  }\n\n  if (dateInfo.isPm === 1 && dateInfo.hour != null && +dateInfo.hour !== 12) {\n    dateInfo.hour = +dateInfo.hour + 12;\n  } else if (dateInfo.isPm === 0 && +dateInfo.hour === 12) {\n    dateInfo.hour = 0;\n  }\n\n  let dateTZ: Date;\n  if (dateInfo.timezoneOffset == null) {\n    dateTZ = new Date(\n      dateInfo.year,\n      dateInfo.month,\n      dateInfo.day,\n      dateInfo.hour,\n      dateInfo.minute,\n      dateInfo.second,\n      dateInfo.millisecond\n    );\n    const validateFields: [\n      \"month\" | \"day\" | \"hour\" | \"minute\" | \"second\",\n      \"getMonth\" | \"getDate\" | \"getHours\" | \"getMinutes\" | \"getSeconds\"\n    ][] = [\n      [\"month\", \"getMonth\"],\n      [\"day\", \"getDate\"],\n      [\"hour\", \"getHours\"],\n      [\"minute\", \"getMinutes\"],\n      [\"second\", \"getSeconds\"]\n    ];\n    for (let i = 0, len = validateFields.length; i < len; i++) {\n      // Check to make sure the date field is within the allowed range. Javascript dates allows values\n      // outside the allowed range. If the values don't match the value was invalid\n      if (\n        specifiedFields[validateFields[i][0]] &&\n        dateInfo[validateFields[i][0]] !== dateTZ[validateFields[i][1]]()\n      ) {\n        return null;\n      }\n    }\n  } else {\n    dateTZ = new Date(\n      Date.UTC(\n        dateInfo.year,\n        dateInfo.month,\n        dateInfo.day,\n        dateInfo.hour,\n        dateInfo.minute - dateInfo.timezoneOffset,\n        dateInfo.second,\n        dateInfo.millisecond\n      )\n    );\n\n    // We can't validate dates in another timezone unfortunately. Do a basic check instead\n    if (\n      dateInfo.month > 11 ||\n      dateInfo.month < 0 ||\n      dateInfo.day > 31 ||\n      dateInfo.day < 1 ||\n      dateInfo.hour > 23 ||\n      dateInfo.hour < 0 ||\n      dateInfo.minute > 59 ||\n      dateInfo.minute < 0 ||\n      dateInfo.second > 59 ||\n      dateInfo.second < 0\n    ) {\n      return null;\n    }\n  }\n\n  // Don't allow invalid dates\n\n  return dateTZ;\n}\nexport default {\n  format,\n  parse,\n  defaultI18n,\n  setGlobalDateI18n,\n  setGlobalDateMasks\n};\nexport { format, parse, defaultI18n, setGlobalDateI18n, setGlobalDateMasks };\n"], "names": [], "mappings": ";;;;;;EAAA,IAAM,KAAK,GAAG,4EAA4E,CAAC;EAC3F,IAAM,iBAAiB,GAAG,SAAS,CAAC;EACpC,IAAM,SAAS,GAAG,QAAQ,CAAC;EAC3B,IAAM,WAAW,GAAG,QAAQ,CAAC;EAC7B,IAAM,UAAU,GAAG,QAAQ,CAAC;EAC5B,IAAM,IAAI,GAAG,SAAS,CAAC;EACvB,IAAM,OAAO,GAAG,eAAe,CAAC;EAyChC,SAAS,OAAO,CAAqB,GAAM,EAAE,IAAY;MACvD,IAAM,MAAM,GAAa,EAAE,CAAC;MAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;UAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;OACrC;MACD,OAAO,MAAM,CAAC;EAChB,CAAC;EAED,IAAM,WAAW,GAAG,UAClB,OAAwE,IACrE,OAAA,UAAC,CAAS,EAAE,IAAkB;MACjC,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,EAAE,GAAA,CAAC,CAAC;MAC7D,IAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;MACpD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,OAAO,KAAK,CAAC;OACd;MACD,OAAO,IAAI,CAAC;EACd,CAAC,GAAA,CAAC;AAMF,WAAgB,MAAM,CAAC,OAAY;MAAE,cAAc;WAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;UAAd,6BAAc;;MACjD,KAAkB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;UAAnB,IAAM,GAAG,aAAA;UACZ,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;;cAErB,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;WACzB;OACF;MACD,OAAO,OAAO,CAAC;EACjB,CAAC;EAED,IAAM,QAAQ,GAAS;MACrB,QAAQ;MACR,QAAQ;MACR,SAAS;MACT,WAAW;MACX,UAAU;MACV,QAAQ;MACR,UAAU;GACX,CAAC;EACF,IAAM,UAAU,GAAW;MACzB,SAAS;MACT,UAAU;MACV,OAAO;MACP,OAAO;MACP,KAAK;MACL,MAAM;MACN,MAAM;MACN,QAAQ;MACR,WAAW;MACX,SAAS;MACT,UAAU;MACV,UAAU;GACX,CAAC;EAEF,IAAM,eAAe,GAAW,OAAO,CAAC,UAAU,EAAE,CAAC,CAAW,CAAC;EACjE,IAAM,aAAa,GAAS,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAS,CAAC;AAEzD,MAAM,WAAW,GAAiB;MAChC,aAAa,eAAA;MACb,QAAQ,UAAA;MACR,eAAe,iBAAA;MACf,UAAU,YAAA;MACV,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MAClB,IAAI,EAAJ,UAAK,UAAkB;UACrB,QACE,UAAU;cACV,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CACtB,UAAU,GAAG,EAAE,GAAG,CAAC;oBACf,CAAC;oBACD,CAAC,CAAC,UAAU,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,UAAU,IAAI,EAAE,CACxE,EACD;OACH;GACF,CAAC;EACF,IAAI,UAAU,GAAG,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;AACzC,MAAM,iBAAiB,GAAG,UAAC,IAA0B;MACnD,QAAC,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC;EAAtC,CAAuC,CAAC;EAE1C,IAAM,WAAW,GAAG,UAAC,GAAW;MAC9B,OAAA,GAAG,CAAC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;EAAxC,CAAwC,CAAC;EAE3C,IAAM,GAAG,GAAG,UAAC,GAAoB,EAAE,GAAO;MAAP,oBAAA,EAAA,OAAO;MACxC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;MAClB,OAAO,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;UACvB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;OACjB;MACD,OAAO,GAAG,CAAC;EACb,CAAC,CAAC;EAEF,IAAM,WAAW,GAGb;MACF,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAA;MACvD,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAA;MACrD,EAAE,EAAE,UAAC,OAAa,EAAE,IAAkB;UACpC,OAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;OAAA;MAC9B,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAA;MACtD,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAA;MACpD,GAAG,EAAE,UAAC,OAAa,EAAE,IAAkB;UACrC,OAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OAAA;MACtC,IAAI,EAAE,UAAC,OAAa,EAAE,IAAkB;UACtC,OAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OAAA;MACjC,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAA;MAC5D,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAA;MAC1D,GAAG,EAAE,UAAC,OAAa,EAAE,IAAkB;UACrC,OAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;OAAA;MAC1C,IAAI,EAAE,UAAC,OAAa,EAAE,IAAkB;UACtC,OAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;OAAA;MACrC,EAAE,EAAE,UAAC,OAAa;UAChB,OAAA,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;OAAA;MACjD,IAAI,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,GAAA;MAC9D,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,GAAA;MACnE,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,GAAA;MACjE,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAA;MACxD,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAA;MACtD,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAA;MAC1D,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAA;MACxD,CAAC,EAAE,UAAC,OAAa,IAAa,OAAA,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAA;MAC1D,EAAE,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAA;MACxD,CAAC,EAAE,UAAC,OAAa;UACf,OAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC,CAAC;OAAA;MACrD,EAAE,EAAE,UAAC,OAAa;UAChB,OAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;OAAA;MACpD,GAAG,EAAE,UAAC,OAAa,IAAa,OAAA,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,GAAA;MACjE,CAAC,EAAE,UAAC,OAAa,EAAE,IAAkB;UACnC,OAAA,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;OAAA;MACvD,CAAC,EAAE,UAAC,OAAa,EAAE,IAAkB;UACnC,OAAA,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;OAAA;MAChC,EAAE,EAAF,UAAG,OAAa;UACd,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;UAC3C,QACE,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;cACvB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EACzE;OACH;MACD,CAAC,EAAD,UAAE,OAAa;UACb,IAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;UAC3C,QACE,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;cACvB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;cACzC,GAAG;cACH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAC7B;OACH;GACF,CAAC;EAQF,IAAM,UAAU,GAAG,UAAC,CAAS,IAAa,OAAA,CAAC,CAAC,GAAG,CAAC,GAAA,CAAC;EACjD,IAAM,WAAW,GAAc,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;EACzD,IAAM,SAAS,GAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC1C,IAAM,IAAI,GAAc;MACtB,MAAM;MACN,IAAI;MACJ,UAAC,CAAS,EAAE,IAAkB;UAC5B,IAAM,GAAG,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;UAC5B,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;cACxB,OAAO,CAAC,CAAC;WACV;eAAM,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;cAC/B,OAAO,CAAC,CAAC;WACV;UACD,OAAO,IAAI,CAAC;OACb;GACF,CAAC;EACF,IAAM,cAAc,GAAc;MAChC,gBAAgB;MAChB,2CAA2C;MAC3C,UAAC,CAAS;UACR,IAAM,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;UAE9C,IAAI,KAAK,EAAE;cACT,IAAM,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;cACxD,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC,OAAO,CAAC;WAC9C;UAED,OAAO,CAAC,CAAC;OACV;GACF,CAAC;EACF,IAAM,UAAU,GAA8B;MAC5C,CAAC,EAAE,CAAC,KAAK,EAAE,iBAAiB,CAAC;MAC7B,EAAE,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;MACtB,EAAE,EAAE,CAAC,KAAK,EAAE,iBAAiB,GAAG,IAAI,EAAE,UAAC,CAAS,IAAa,OAAA,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,GAAA,CAAC;MAC7E,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,UAAU,CAAC;MAC3C,EAAE,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;MACpC,EAAE,EAAE;UACF,MAAM;UACN,SAAS;UACT,UAAC,CAAS;cACR,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;cACvB,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACpD,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;WAChD;OACF;MACD,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,CAAC;MACjD,EAAE,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;MAC1C,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC;MAC9B,EAAE,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;MACvB,CAAC,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;MAChC,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;MACzB,CAAC,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;MAChC,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;MACzB,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;MAC1B,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,UAAC,CAAS,IAAa,OAAA,CAAC,CAAC,GAAG,GAAG,GAAA,CAAC;MAC1D,EAAE,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,UAAC,CAAS,IAAa,OAAA,CAAC,CAAC,GAAG,EAAE,GAAA,CAAC;MAC9D,GAAG,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;MACjC,CAAC,EAAE,WAAW;MACd,EAAE,EAAE,WAAW;MACf,GAAG,EAAE,SAAS;MACd,IAAI,EAAE,SAAS;MACf,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC;MACpD,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC;MAChD,CAAC,EAAE,IAAI;MACP,CAAC,EAAE,IAAI;MACP,EAAE,EAAE,cAAc;MAClB,CAAC,EAAE,cAAc;GAClB,CAAC;EAEF;EACA,IAAM,WAAW,GAA8B;MAC7C,OAAO,EAAE,0BAA0B;MACnC,SAAS,EAAE,QAAQ;MACnB,UAAU,EAAE,aAAa;MACzB,QAAQ,EAAE,cAAc;MACxB,QAAQ,EAAE,oBAAoB;MAC9B,OAAO,EAAE,YAAY;MACrB,WAAW,EAAE,sBAAsB;MACnC,SAAS,EAAE,OAAO;MAClB,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE,cAAc;GACzB,CAAC;AACF,MAAM,kBAAkB,GAAG,UAAC,KAE3B,IAAgC,OAAA,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,GAAA,CAAC;EAE5D;;;;;;;AAOA,MAAM,MAAM,GAAG,UACb,OAAa,EACb,IAAqC,EACrC,IAA+B;MAD/B,qBAAA,EAAA,OAAe,WAAW,CAAC,SAAS,CAAC;MACrC,qBAAA,EAAA,SAA+B;MAE/B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;UAC/B,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;OAC7B;MAED,IACE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,eAAe;UAC3D,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EACxB;UACA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;OAChD;MAED,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;MAEjC,IAAM,QAAQ,GAAa,EAAE,CAAC;;MAG9B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,EAAE,EAAE,EAAE;UAC1C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UAClB,OAAO,KAAK,CAAC;OACd,CAAC,CAAC;MAEH,IAAM,oBAAoB,GAAiB,MAAM,CAC/C,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,EACtB,IAAI,CACL,CAAC;;MAEF,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAA,EAAE;UAC3B,OAAA,WAAW,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,oBAAoB,CAAC;OAAA,CAC/C,CAAC;;MAEF,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,cAAM,OAAA,QAAQ,CAAC,KAAK,EAAE,GAAA,CAAC,CAAC;EACtD,CAAC,CAAC;EAEF;;;;;;;;EAQA,SAAS,KAAK,CACZ,OAAe,EACf,MAAc,EACd,IAA+B;MAA/B,qBAAA,EAAA,SAA+B;MAE/B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;UAC9B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;OAClD;;MAGD,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC;;;MAIvC,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE;UACzB,OAAO,IAAI,CAAC;OACb;;MAGD,IAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;MACzB,IAAM,QAAQ,GAAa;UACzB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE;UACzB,KAAK,EAAE,CAAC;UACR,GAAG,EAAE,CAAC;UACN,IAAI,EAAE,CAAC;UACP,MAAM,EAAE,CAAC;UACT,MAAM,EAAE,CAAC;UACT,WAAW,EAAE,CAAC;UACd,IAAI,EAAE,IAAI;UACV,cAAc,EAAE,IAAI;OACrB,CAAC;MACF,IAAM,SAAS,GAAgB,EAAE,CAAC;MAClC,IAAM,QAAQ,GAAa,EAAE,CAAC;;MAG9B,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,EAAE;UAC7C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;UAC/B,OAAO,KAAK,CAAC;OACd,CAAC,CAAC;MACH,IAAM,eAAe,GAAiC,EAAE,CAAC;MACzD,IAAM,cAAc,GAAiC,EAAE,CAAC;;MAGxD,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,UAAA,EAAE;UAClD,IAAM,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;UACrB,IAAA,KAAK,GAA4B,IAAI,GAAhC,EAAE,KAAK,GAAqB,IAAI,GAAzB,EAAI,aAAa,GAAI,IAAI,GAAR,CAAS;;UAG7C,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;cAC1B,MAAM,IAAI,KAAK,CAAC,qBAAmB,KAAK,+BAA4B,CAAC,CAAC;WACvE;UAED,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;;UAG9B,IAAI,aAAa,EAAE;cACjB,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;WACtC;UAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACrB,OAAO,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;OAC1B,CAAC,CAAC;;MAGH,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;UACvC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;cAC3B,MAAM,IAAI,KAAK,CACb,qBAAmB,KAAK,qCAAkC,CAC3D,CAAC;WACH;OACF,CAAC,CAAC;;MAGH,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,cAAM,OAAA,QAAQ,CAAC,KAAK,EAAE,GAAA,CAAC,CAAC;;MAG9D,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;MAC1D,IAAI,CAAC,OAAO,EAAE;UACZ,OAAO,IAAI,CAAC;OACb;MAED,IAAM,oBAAoB,GAAiB,MAAM,CAC/C,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,EACtB,IAAI,CACL,CAAC;;MAGF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;UACjC,IAAA,KAAoB,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAnC,KAAK,QAAA,EAAI,MAAM,QAAoB,CAAC;UAC3C,IAAM,KAAK,GAAG,MAAM;gBAChB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC;gBACxC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;UAGhB,IAAI,KAAK,IAAI,IAAI,EAAE;cACjB,OAAO,IAAI,CAAC;WACb;UAED,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;OACzB;MAED,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,EAAE,EAAE;UACzE,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;OACrC;WAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,EAAE,EAAE;UACvD,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;OACnB;MAED,IAAI,MAAY,CAAC;MACjB,IAAI,QAAQ,CAAC,cAAc,IAAI,IAAI,EAAE;UACnC,MAAM,GAAG,IAAI,IAAI,CACf,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,GAAG,EACZ,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,WAAW,CACrB,CAAC;UACF,IAAM,cAAc,GAGd;cACJ,CAAC,OAAO,EAAE,UAAU,CAAC;cACrB,CAAC,KAAK,EAAE,SAAS,CAAC;cAClB,CAAC,MAAM,EAAE,UAAU,CAAC;cACpB,CAAC,QAAQ,EAAE,YAAY,CAAC;cACxB,CAAC,QAAQ,EAAE,YAAY,CAAC;WACzB,CAAC;UACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;;;cAGzD,IACE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EACjE;kBACA,OAAO,IAAI,CAAC;eACb;WACF;OACF;WAAM;UACL,MAAM,GAAG,IAAI,IAAI,CACf,IAAI,CAAC,GAAG,CACN,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,GAAG,EACZ,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,cAAc,EACzC,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,WAAW,CACrB,CACF,CAAC;;UAGF,IACE,QAAQ,CAAC,KAAK,GAAG,EAAE;cACnB,QAAQ,CAAC,KAAK,GAAG,CAAC;cAClB,QAAQ,CAAC,GAAG,GAAG,EAAE;cACjB,QAAQ,CAAC,GAAG,GAAG,CAAC;cAChB,QAAQ,CAAC,IAAI,GAAG,EAAE;cAClB,QAAQ,CAAC,IAAI,GAAG,CAAC;cACjB,QAAQ,CAAC,MAAM,GAAG,EAAE;cACpB,QAAQ,CAAC,MAAM,GAAG,CAAC;cACnB,QAAQ,CAAC,MAAM,GAAG,EAAE;cACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,EACnB;cACA,OAAO,IAAI,CAAC;WACb;OACF;;MAID,OAAO,MAAM,CAAC;EAChB,CAAC;AACD,cAAe;MACb,MAAM,QAAA;MACN,KAAK,OAAA;MACL,WAAW,aAAA;MACX,iBAAiB,mBAAA;MACjB,kBAAkB,oBAAA;GACnB,CAAC;;;;;;;;;;;;;;;;;;"}