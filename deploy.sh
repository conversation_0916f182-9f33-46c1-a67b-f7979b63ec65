#!/bin/bash

# Kontour Coin Deployment Script
echo "🚀 Starting Kontour Coin Platform Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) detected"

# Install dependencies
print_status "Installing dependencies..."
npm install --legacy-peer-deps

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

print_success "Dependencies installed successfully"

# Create a simple static build without TypeScript checking
print_status "Creating static build..."

# Create out directory
mkdir -p out

# Copy static HTML files
print_status "Copying static files..."
cp index.html out/
cp sitemap.html out/
cp pathway-flowchart.html out/
cp demo-dashboard.html out/
cp deployment-status.html out/

# Copy public directory if it exists
if [ -d "public" ]; then
    cp -r public/* out/ 2>/dev/null || true
fi

# Create a simple package.json for the static site
cat > out/package.json << EOF
{
  "name": "kontour-coin-static",
  "version": "1.0.0",
  "description": "Kontour Coin Static Website",
  "main": "index.html",
  "scripts": {
    "start": "serve .",
    "dev": "serve ."
  }
}
EOF

print_success "Static build created in 'out' directory"

# Deployment options
echo ""
echo "🌐 Deployment Options:"
echo "1. Local Server (serve)"
echo "2. Docker Container"
echo "3. GitHub Pages"
echo "4. Netlify"
echo "5. Vercel"
echo "6. Exit"

read -p "Choose deployment option (1-6): " choice

case $choice in
    1)
        print_status "Starting local server..."
        if ! command -v serve &> /dev/null; then
            print_status "Installing serve globally..."
            npm install -g serve
        fi
        cd out
        print_success "Server starting at http://localhost:3000"
        serve -s . -l 3000
        ;;
    2)
        print_status "Building Docker container..."
        docker build -t kontour-coin .
        if [ $? -eq 0 ]; then
            print_success "Docker image built successfully"
            print_status "Starting container..."
            docker run -p 3000:3000 kontour-coin
        else
            print_error "Docker build failed"
        fi
        ;;
    3)
        print_status "Preparing for GitHub Pages deployment..."
        print_warning "Make sure to:"
        echo "1. Push your code to GitHub"
        echo "2. Go to repository Settings > Pages"
        echo "3. Select 'Deploy from a branch'"
        echo "4. Choose 'main' branch and '/out' folder"
        echo "5. Or run: npm run deploy:github"
        ;;
    4)
        print_status "Preparing for Netlify deployment..."
        if command -v netlify &> /dev/null; then
            netlify deploy --dir=out --prod
        else
            print_warning "Netlify CLI not found. Install with: npm install -g netlify-cli"
            print_status "Or drag and drop the 'out' folder to https://app.netlify.com/drop"
        fi
        ;;
    5)
        print_status "Preparing for Vercel deployment..."
        if command -v vercel &> /dev/null; then
            vercel --prod
        else
            print_warning "Vercel CLI not found. Install with: npm install -g vercel"
            print_status "Or drag and drop the 'out' folder to https://vercel.com/new"
        fi
        ;;
    6)
        print_status "Deployment cancelled"
        exit 0
        ;;
    *)
        print_error "Invalid option"
        exit 1
        ;;
esac

print_success "Deployment process completed!"
echo ""
echo "📁 Static files are available in the 'out' directory"
echo "🌐 You can also manually deploy these files to any static hosting service"
echo ""
echo "🔗 Available pages:"
echo "   • index.html - Main landing page"
echo "   • sitemap.html - Complete sitemap"
echo "   • pathway-flowchart.html - Architecture flowchart"
echo "   • demo-dashboard.html - System dashboard"
echo "   • deployment-status.html - Deployment status"
