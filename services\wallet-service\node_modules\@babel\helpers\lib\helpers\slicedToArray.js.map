{"version": 3, "names": ["_arrayWithHoles", "require", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_slicedToArray", "arr", "i", "arrayWithHoles", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest"], "sources": ["../../src/helpers/slicedToArray.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport arrayWithHoles from \"./arrayWithHoles.ts\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.ts\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.ts\";\n// @ts-expect-error nonIterableRest is still being converted to TS.\nimport nonIterableRest from \"./nonIterableRest.ts\";\n\nexport default function _slicedToArray<T>(arr: any, i: number): T[] {\n  return (\n    arrayWithHoles<T>(arr) ||\n    iterableToArrayLimit<T>(arr, i) ||\n    unsupportedIterableToArray<T>(arr, i) ||\n    nonIterableRest()\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,2BAAA,GAAAF,OAAA;AAEA,IAAAG,gBAAA,GAAAH,OAAA;AAEe,SAASI,cAAcA,CAAIC,GAAQ,EAAEC,CAAS,EAAO;EAClE,OACE,IAAAC,uBAAc,EAAIF,GAAG,CAAC,IACtB,IAAAG,6BAAoB,EAAIH,GAAG,EAAEC,CAAC,CAAC,IAC/B,IAAAG,mCAA0B,EAAIJ,GAAG,EAAEC,CAAC,CAAC,IACrC,IAAAI,wBAAe,EAAC,CAAC;AAErB", "ignoreList": []}