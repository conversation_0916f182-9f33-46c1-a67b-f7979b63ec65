/* Real-time UI Styles */

/* Connection Status */
.connection-status {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.connection-status.disconnected {
    background: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.connection-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #EF4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Real-time Update Animations */
.price-update {
    animation: priceFlash 1s ease-out;
}

.value-update {
    animation: valueFlash 1s ease-out;
}

.metric-update {
    animation: metricPulse 1s ease-out;
}

@keyframes priceFlash {
    0% {
        background: rgba(139, 92, 246, 0.3);
        transform: scale(1);
    }
    50% {
        background: rgba(139, 92, 246, 0.5);
        transform: scale(1.05);
    }
    100% {
        background: transparent;
        transform: scale(1);
    }
}

@keyframes valueFlash {
    0% {
        color: var(--primary-purple);
        transform: scale(1);
    }
    50% {
        color: var(--secondary-purple);
        transform: scale(1.1);
    }
    100% {
        color: inherit;
        transform: scale(1);
    }
}

@keyframes metricPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* Live Data Indicators */
.live-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--success);
    margin-left: 0.5rem;
}

.live-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: livePulse 2s infinite;
}

@keyframes livePulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* Real-time Charts */
.realtime-chart {
    position: relative;
    background: var(--white);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chart-title {
    font-weight: 600;
    color: var(--text-dark);
}

.chart-timeframe {
    display: flex;
    gap: 0.5rem;
}

.timeframe-btn {
    padding: 0.25rem 0.75rem;
    border: 1px solid rgba(139, 92, 246, 0.3);
    background: none;
    border-radius: 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeframe-btn.active {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-purple);
}

.timeframe-btn:hover {
    background: rgba(139, 92, 246, 0.1);
}

/* Market Data Grid */
.market-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.market-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
    transition: all 0.3s ease;
}

.market-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.market-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.token-symbol {
    font-weight: 600;
    color: var(--text-dark);
}

.token-icon {
    font-size: 1.5rem;
}

.token-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.token-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.token-change.positive {
    color: var(--success);
}

.token-change.negative {
    color: var(--error);
}

/* Activity Feed */
.activity-feed {
    background: var(--white);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
    max-height: 400px;
    overflow-y: auto;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(139, 92, 246, 0.05);
    animation: slideInRight 0.3s ease-out;
}

.activity-item:last-child {
    border-bottom: none;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: var(--white);
}

.activity-icon.swap {
    background: var(--gradient-primary);
}

.activity-icon.agent {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.activity-icon.workflow {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.875rem;
    color: var(--text-light);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-light);
}

/* Performance Metrics */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.performance-card {
    background: var(--gradient-secondary);
    border-radius: 12px;
    padding: 1rem;
    color: var(--white);
    text-align: center;
}

.performance-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.performance-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.performance-change {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* System Status */
.system-status {
    background: var(--white);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.status-item {
    text-align: center;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(139, 92, 246, 0.05);
}

.status-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.status-label {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.status-value {
    font-weight: 600;
    color: var(--text-dark);
}

.status-healthy {
    color: var(--success);
}

.status-warning {
    color: var(--warning);
}

.status-error {
    color: var(--error);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.loading-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-md);
}

.loading-line {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.loading-line.short {
    width: 60%;
}

.loading-line.medium {
    width: 80%;
}

.loading-line.long {
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .connection-status {
        top: 70px;
        right: 10px;
        font-size: 0.75rem;
        padding: 6px 12px;
    }
    
    .market-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .performance-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .status-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
    
    .activity-feed {
        max-height: 300px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .realtime-chart,
    .market-card,
    .activity-feed,
    .system-status,
    .loading-card {
        background: #1f2937;
        border-color: rgba(139, 92, 246, 0.2);
    }
    
    .chart-title,
    .token-symbol,
    .token-price,
    .activity-title,
    .status-value {
        color: #f9fafb;
    }
    
    .activity-description,
    .activity-time,
    .status-label {
        color: #d1d5db;
    }
}
