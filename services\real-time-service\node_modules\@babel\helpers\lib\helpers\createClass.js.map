{"version": 3, "names": ["_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype"], "sources": ["../../src/helpers/createClass.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport toPropertyKey from \"./toPropertyKey.ts\";\n\ninterface Prop extends PropertyDescriptor {\n  key: PropertyKey;\n}\n\nfunction _defineProperties(target: object, props: Prop[]): void {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\n\nexport default function _createClass<T extends new (...args: any[]) => any>(\n  Constructor: T,\n  protoProps?: Prop[],\n  staticProps?: Prop[],\n): T {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", { writable: false });\n  return Constructor;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAMA,SAASC,iBAAiBA,CAACC,MAAc,EAAEC,KAAa,EAAQ;EAC9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAE,IAAAU,sBAAa,EAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAC1E;AACF;AAEe,SAASQ,YAAYA,CAClCC,WAAc,EACdC,UAAmB,EACnBC,WAAoB,EACjB;EACH,IAAID,UAAU,EAAEf,iBAAiB,CAACc,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEhB,iBAAiB,CAACc,WAAW,EAAEE,WAAW,CAAC;EAC5DP,MAAM,CAACC,cAAc,CAACI,WAAW,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EACpE,OAAOM,WAAW;AACpB", "ignoreList": []}