{"version": 3, "names": ["_regeneratorValues", "iterable", "iteratorMethod", "Symbol", "iterator", "i", "call", "next", "isNaN", "length", "undefined", "value", "done", "TypeError"], "sources": ["../../src/helpers/regeneratorValues.ts"], "sourcesContent": ["/* @minVersion 7.18.0 */\n/* @mangleFns */\n\nexport default function _regeneratorValues(iterable: any) {\n  if (iterable != null) {\n    var iteratorMethod =\n        iterable[\n          (typeof Symbol === \"function\" && Symbol.iterator) || \"@@iterator\"\n        ],\n      i = 0;\n\n    if (iteratorMethod) {\n      return iteratorMethod.call(iterable);\n    }\n\n    if (typeof iterable.next === \"function\") {\n      return iterable;\n    }\n\n    if (!isNaN(iterable.length)) {\n      return {\n        next: function () {\n          if (iterable && i >= iterable.length) iterable = undefined;\n          return { value: iterable && iterable[i++], done: !iterable };\n        },\n      };\n    }\n  }\n\n  throw new TypeError(typeof iterable + \" is not iterable\");\n}\n"], "mappings": ";;;;;;AAGe,SAASA,kBAAkBA,CAACC,QAAa,EAAE;EACxD,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAIC,cAAc,GACdD,QAAQ,CACL,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ,IAAK,YAAY,CAClE;MACHC,CAAC,GAAG,CAAC;IAEP,IAAIH,cAAc,EAAE;MAClB,OAAOA,cAAc,CAACI,IAAI,CAACL,QAAQ,CAAC;IACtC;IAEA,IAAI,OAAOA,QAAQ,CAACM,IAAI,KAAK,UAAU,EAAE;MACvC,OAAON,QAAQ;IACjB;IAEA,IAAI,CAACO,KAAK,CAACP,QAAQ,CAACQ,MAAM,CAAC,EAAE;MAC3B,OAAO;QACLF,IAAI,EAAE,SAAAA,CAAA,EAAY;UAChB,IAAIN,QAAQ,IAAII,CAAC,IAAIJ,QAAQ,CAACQ,MAAM,EAAER,QAAQ,GAAGS,SAAS;UAC1D,OAAO;YAAEC,KAAK,EAAEV,QAAQ,IAAIA,QAAQ,CAACI,CAAC,EAAE,CAAC;YAAEO,IAAI,EAAE,CAACX;UAAS,CAAC;QAC9D;MACF,CAAC;IACH;EACF;EAEA,MAAM,IAAIY,SAAS,CAAC,OAAOZ,QAAQ,GAAG,kBAAkB,CAAC;AAC3D", "ignoreList": []}