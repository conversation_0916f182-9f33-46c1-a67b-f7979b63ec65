{"name": "type-detect", "description": "Improved typeof detection for node.js and the browser.", "keywords": ["type", "typeof", "types"], "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://alogicalparadox.com)", "contributors": ["<PERSON> (https://github.com/keithamus)", "<PERSON> (https://github.com/davelosert)", "<PERSON><PERSON><PERSON> (https://github.com/shvaikalesh)", "<PERSON> (https://github.com/lucasfcosta)", "<PERSON> (https://github.com/meeber)", "<PERSON> (https://github.com/jetpacmonkey)", "<PERSON> (https://github.com/Edward<PERSON>etts)", "dvlsg (https://github.com/dvlsg)", "<PERSON><PERSON> (https://github.com/amilajack)", "<PERSON> (https://github.com/Jake<PERSON>ham<PERSON>)", "<PERSON><PERSON><PERSON> (https://github.com/bajtos)"], "files": ["index.js", "type-detect.js"], "main": "./type-detect.js", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/type-detect.git"}, "scripts": {"bench": "node bench", "build": "rollup -c rollup.conf.js", "commit-msg": "commitlint -x angular", "lint": "eslint --ignore-path .gitignore .", "prepare": "cross-env NODE_ENV=production npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest:node": "cross-env NODE_ENV=test npm run build", "pretest:browser": "cross-env NODE_ENV=test npm run build", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start --singleRun=true", "test:node": "nyc mocha type-detect.test.js", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "posttest:browser": "npm run upload-coverage", "upload-coverage": "codecov"}, "eslintConfig": {"env": {"es6": true}, "extends": ["strict/es6"], "globals": {"HTMLElement": false}, "rules": {"complexity": 0, "max-statements": 0, "prefer-rest-params": 0}}, "devDependencies": {"@commitlint/cli": "^4.2.2", "benchmark": "^2.1.0", "buble": "^0.16.0", "codecov": "^3.0.0", "commitlint-config-angular": "^4.2.1", "cross-env": "^5.1.1", "eslint": "^4.10.0", "eslint-config-strict": "^14.0.0", "eslint-plugin-filenames": "^1.2.0", "husky": "^0.14.3", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-detect-browsers": "^2.2.5", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^1.0.1", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-opera-launcher": "^1.0.0", "karma-safari-launcher": "^1.0.0", "karma-safaritechpreview-launcher": "0.0.6", "karma-sauce-launcher": "^1.2.0", "mocha": "^4.0.1", "nyc": "^11.3.0", "rollup": "^0.50.0", "rollup-plugin-buble": "^0.16.0", "rollup-plugin-commonjs": "^8.2.6", "rollup-plugin-istanbul": "^1.1.0", "rollup-plugin-node-resolve": "^3.0.0", "semantic-release": "^8.2.0", "simple-assert": "^1.0.0"}, "engines": {"node": ">=4"}, "version": "4.0.8"}