@echo off
echo ========================================
echo 🚀 KONTOUR COIN FULL-STACK DEPLOYMENT
echo ========================================
echo.

echo 💎 Starting Kontour Coin Full-Stack Platform...
echo.

REM Create logs directory
if not exist "logs" mkdir logs

echo 📊 Starting API Gateway (Port 8080)...
start "API Gateway" cmd /k "cd services\api-gateway && npm install && node server.js"
timeout /t 3 /nobreak >nul

echo 💰 Starting Wallet Service (Port 3001)...
start "Wallet Service" cmd /k "cd services\wallet-service && npm install && node server.js"
timeout /t 3 /nobreak >nul

echo ⚡ Starting Real-time Service (Port 8035)...
start "Real-time Service" cmd /k "cd services\real-time-service && npm install && node server.js"
timeout /t 3 /nobreak >nul

echo 🤖 Starting Agentic AI Service (Port 8070)...
start "Agentic AI" cmd /k "cd services\agentic-ai-service && python agentic_ai_service.py"
timeout /t 3 /nobreak >nul

echo 🧠 Starting Neural Network Service (Port 8050)...
start "Neural Networks" cmd /k "cd services\neural-network-service && python neural_network_service.py"
timeout /t 3 /nobreak >nul

echo 📊 Starting Big Data Service (Port 8040)...
start "Big Data" cmd /k "cd services\big-data-service && python big_data_service.py"
timeout /t 3 /nobreak >nul

echo 🌐 Starting IoT Processing Service (Port 8060)...
start "IoT Processing" cmd /k "cd services\iot-processing-service && python iot_processing_service.py"
timeout /t 3 /nobreak >nul

echo 🧠 Starting AI Service (Port 8020)...
start "AI Service" cmd /k "cd services\ai-service && python ai_service.py"
timeout /t 3 /nobreak >nul

echo 🔗 Starting Integration Service (Port 8010)...
start "Integration Service" cmd /k "cd services\integration-service && python integration_service.py"
timeout /t 3 /nobreak >nul

echo 🌐 Starting Website (Port 3005)...
start "Kontour Website" cmd /k "cd website && npm install && node server.js"
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ FULL-STACK DEPLOYMENT COMPLETE!
echo ========================================
echo.
echo 🌐 Main Website: http://localhost:3005
echo 💎 Wallet Interface: http://localhost:3005#wallet
echo 📊 API Gateway: http://localhost:8080
echo 💰 Wallet Service: http://localhost:3001
echo ⚡ Real-time Service: http://localhost:8035
echo 🤖 Agentic AI: http://localhost:8070
echo.
echo 📋 Service Status Dashboard: http://localhost:3005#services
echo 📚 API Documentation: http://localhost:8080/api/docs
echo.
echo ========================================
echo 🎯 KONTOUR COIN FEATURES AVAILABLE:
echo ========================================
echo.
echo 💎 Purple Gem Branding - Complete visual identity
echo 💰 Multi-chain Wallet - KONTOUR, ETH, BTC, USDC, USDT, SOL
echo 🔄 Token Swap - Real-time swapping with 0.3%% fees
echo 🤖 Agentic AI - 5 types of autonomous agents
echo 🧠 Neural Networks - Deep learning and AI models
echo 📊 Big Data - Apache Spark data processing
echo 🌐 IoT Integration - Device management and monitoring
echo ⚡ Real-time Updates - Live WebSocket connections
echo 🏦 DeFi Pools - Liquidity provision and yield farming
echo 📈 Live Analytics - Real-time market data and charts
echo.
echo Press any key to open the main website...
pause >nul

start http://localhost:3005

echo.
echo 🎉 Welcome to Kontour Coin!
echo Your complete AI-powered cryptocurrency platform is now running.
echo.
pause
