const express = require('express');
const cors = require('cors');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 8000;

// Configure Winston logger
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/workflow.log' })
    ]
});

// Middleware
app.use(cors());
app.use(express.json());

// Workflow storage
let workflows = new Map();
let workflowInstances = new Map();
let workflowTemplates = new Map();

// Service endpoints
const services = {
    wallet: 'http://localhost:3001',
    realtime: 'http://localhost:8035',
    agenticAI: 'http://localhost:8070',
    neuralNetwork: 'http://localhost:8050',
    bigData: 'http://localhost:8040',
    iot: 'http://localhost:8060',
    ai: 'http://localhost:8020',
    integration: 'http://localhost:8010'
};

// Initialize workflow templates
initializeWorkflowTemplates();

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'kontour-workflow-orchestrator',
        timestamp: new Date().toISOString(),
        activeWorkflows: workflowInstances.size,
        totalTemplates: workflowTemplates.size,
        uptime: process.uptime()
    });
});

// Workflow Templates
app.get('/api/workflows/templates', (req, res) => {
    try {
        const templates = Array.from(workflowTemplates.values());
        res.json({
            success: true,
            templates,
            total: templates.length
        });
    } catch (error) {
        logger.error('Error fetching workflow templates:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Create workflow from template
app.post('/api/workflows/create', async (req, res) => {
    try {
        const { templateId, name, parameters = {} } = req.body;
        
        const template = workflowTemplates.get(templateId);
        if (!template) {
            return res.status(404).json({ success: false, error: 'Template not found' });
        }
        
        const workflowId = uuidv4();
        const workflow = {
            id: workflowId,
            name: name || template.name,
            templateId,
            parameters,
            status: 'created',
            steps: template.steps.map(step => ({
                ...step,
                id: uuidv4(),
                status: 'pending',
                result: null,
                startTime: null,
                endTime: null
            })),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            progress: 0
        };
        
        workflows.set(workflowId, workflow);
        
        logger.info(`Workflow created: ${workflowId}`);
        
        res.json({
            success: true,
            workflow,
            message: 'Workflow created successfully'
        });
        
    } catch (error) {
        logger.error('Error creating workflow:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Execute workflow
app.post('/api/workflows/:workflowId/execute', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const workflow = workflows.get(workflowId);
        
        if (!workflow) {
            return res.status(404).json({ success: false, error: 'Workflow not found' });
        }
        
        if (workflow.status === 'running') {
            return res.status(400).json({ success: false, error: 'Workflow already running' });
        }
        
        // Start workflow execution
        workflow.status = 'running';
        workflow.startTime = new Date().toISOString();
        workflow.updatedAt = new Date().toISOString();
        
        workflowInstances.set(workflowId, workflow);
        
        // Execute workflow asynchronously
        executeWorkflow(workflowId);
        
        res.json({
            success: true,
            workflowId,
            status: 'running',
            message: 'Workflow execution started'
        });
        
    } catch (error) {
        logger.error('Error executing workflow:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get workflow status
app.get('/api/workflows/:workflowId', (req, res) => {
    try {
        const { workflowId } = req.params;
        const workflow = workflows.get(workflowId) || workflowInstances.get(workflowId);
        
        if (!workflow) {
            return res.status(404).json({ success: false, error: 'Workflow not found' });
        }
        
        res.json({
            success: true,
            workflow
        });
        
    } catch (error) {
        logger.error('Error fetching workflow:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// List all workflows
app.get('/api/workflows', (req, res) => {
    try {
        const allWorkflows = [
            ...Array.from(workflows.values()),
            ...Array.from(workflowInstances.values())
        ];
        
        res.json({
            success: true,
            workflows: allWorkflows,
            total: allWorkflows.length
        });
        
    } catch (error) {
        logger.error('Error listing workflows:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Workflow execution engine
async function executeWorkflow(workflowId) {
    try {
        const workflow = workflowInstances.get(workflowId);
        if (!workflow) return;
        
        logger.info(`Starting workflow execution: ${workflowId}`);
        
        for (let i = 0; i < workflow.steps.length; i++) {
            const step = workflow.steps[i];
            
            try {
                step.status = 'running';
                step.startTime = new Date().toISOString();
                workflow.updatedAt = new Date().toISOString();
                
                // Notify real-time service
                notifyRealTimeService('workflow_step_started', {
                    workflowId,
                    stepId: step.id,
                    stepName: step.name
                });
                
                // Execute step
                const result = await executeWorkflowStep(step, workflow.parameters);
                
                step.status = 'completed';
                step.result = result;
                step.endTime = new Date().toISOString();
                
                // Update progress
                workflow.progress = Math.round(((i + 1) / workflow.steps.length) * 100);
                workflow.updatedAt = new Date().toISOString();
                
                // Notify completion
                notifyRealTimeService('workflow_step_completed', {
                    workflowId,
                    stepId: step.id,
                    stepName: step.name,
                    result
                });
                
                logger.info(`Step completed: ${step.name} in workflow ${workflowId}`);
                
            } catch (error) {
                step.status = 'failed';
                step.error = error.message;
                step.endTime = new Date().toISOString();
                
                workflow.status = 'failed';
                workflow.endTime = new Date().toISOString();
                workflow.updatedAt = new Date().toISOString();
                
                logger.error(`Step failed: ${step.name} in workflow ${workflowId}`, error);
                
                notifyRealTimeService('workflow_failed', {
                    workflowId,
                    stepId: step.id,
                    error: error.message
                });
                
                return;
            }
        }
        
        // Workflow completed successfully
        workflow.status = 'completed';
        workflow.endTime = new Date().toISOString();
        workflow.progress = 100;
        workflow.updatedAt = new Date().toISOString();
        
        logger.info(`Workflow completed: ${workflowId}`);
        
        notifyRealTimeService('workflow_completed', {
            workflowId,
            duration: new Date(workflow.endTime) - new Date(workflow.startTime)
        });
        
    } catch (error) {
        logger.error(`Workflow execution failed: ${workflowId}`, error);
    }
}

// Execute individual workflow step
async function executeWorkflowStep(step, parameters) {
    switch (step.type) {
        case 'wallet_operation':
            return await executeWalletOperation(step, parameters);
        case 'ai_analysis':
            return await executeAIAnalysis(step, parameters);
        case 'data_processing':
            return await executeDataProcessing(step, parameters);
        case 'agent_task':
            return await executeAgentTask(step, parameters);
        case 'swap_execution':
            return await executeSwapOperation(step, parameters);
        case 'notification':
            return await executeNotification(step, parameters);
        default:
            throw new Error(`Unknown step type: ${step.type}`);
    }
}

// Step execution functions
async function executeWalletOperation(step, parameters) {
    const { operation, token, amount, address } = step.config;
    
    try {
        const response = await axios.post(`${services.wallet}/api/wallet/${operation}`, {
            token,
            amount,
            address,
            ...parameters
        });
        
        return response.data;
    } catch (error) {
        throw new Error(`Wallet operation failed: ${error.message}`);
    }
}

async function executeAIAnalysis(step, parameters) {
    const { analysisType, data } = step.config;
    
    try {
        const response = await axios.post(`${services.ai}/api/analysis/${analysisType}`, {
            data,
            ...parameters
        });
        
        return response.data;
    } catch (error) {
        throw new Error(`AI analysis failed: ${error.message}`);
    }
}

async function executeDataProcessing(step, parameters) {
    const { processingType, dataset } = step.config;
    
    try {
        const response = await axios.post(`${services.bigData}/api/process/${processingType}`, {
            dataset,
            ...parameters
        });
        
        return response.data;
    } catch (error) {
        throw new Error(`Data processing failed: ${error.message}`);
    }
}

async function executeAgentTask(step, parameters) {
    const { agentType, task, taskParameters } = step.config;
    
    try {
        const response = await axios.post(`${services.agenticAI}/api/agents/execute`, {
            agentType,
            task,
            parameters: { ...taskParameters, ...parameters }
        });
        
        return response.data;
    } catch (error) {
        throw new Error(`Agent task failed: ${error.message}`);
    }
}

async function executeSwapOperation(step, parameters) {
    const { fromToken, toToken, amount } = step.config;
    
    try {
        // Get quote first
        const quoteResponse = await axios.post(`${services.wallet}/api/swap/quote`, {
            fromToken,
            toToken,
            amount,
            ...parameters
        });
        
        // Execute swap
        const swapResponse = await axios.post(`${services.wallet}/api/swap/execute`, {
            quoteId: quoteResponse.data.quote.quoteId,
            ...parameters
        });
        
        return swapResponse.data;
    } catch (error) {
        throw new Error(`Swap operation failed: ${error.message}`);
    }
}

async function executeNotification(step, parameters) {
    const { message, type, recipients } = step.config;
    
    try {
        await notifyRealTimeService('workflow_notification', {
            message,
            type,
            recipients,
            ...parameters
        });
        
        return { success: true, message: 'Notification sent' };
    } catch (error) {
        throw new Error(`Notification failed: ${error.message}`);
    }
}

// Real-time service notification
async function notifyRealTimeService(event, data) {
    try {
        await axios.post(`${services.realtime}/api/notify`, {
            event,
            data,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.warn('Failed to notify real-time service:', error.message);
    }
}

// Initialize workflow templates
function initializeWorkflowTemplates() {
    // Automated Trading Workflow
    workflowTemplates.set('auto-trading', {
        id: 'auto-trading',
        name: 'Automated Trading Workflow',
        description: 'AI-powered automated trading with risk management',
        category: 'trading',
        steps: [
            {
                name: 'Market Analysis',
                type: 'ai_analysis',
                config: { analysisType: 'market_sentiment', data: 'current_market' }
            },
            {
                name: 'Risk Assessment',
                type: 'agent_task',
                config: { agentType: 'validator', task: 'assess_risk' }
            },
            {
                name: 'Execute Trade',
                type: 'swap_execution',
                config: { fromToken: 'USDC', toToken: 'KONTOUR', amount: '1000' }
            },
            {
                name: 'Update Portfolio',
                type: 'wallet_operation',
                config: { operation: 'update_portfolio' }
            },
            {
                name: 'Send Notification',
                type: 'notification',
                config: { message: 'Trade executed successfully', type: 'success' }
            }
        ]
    });
    
    // Portfolio Rebalancing Workflow
    workflowTemplates.set('portfolio-rebalance', {
        id: 'portfolio-rebalance',
        name: 'Portfolio Rebalancing',
        description: 'Automatic portfolio rebalancing based on target allocations',
        category: 'portfolio',
        steps: [
            {
                name: 'Analyze Current Portfolio',
                type: 'wallet_operation',
                config: { operation: 'get_portfolio_analysis' }
            },
            {
                name: 'Calculate Rebalancing',
                type: 'ai_analysis',
                config: { analysisType: 'portfolio_optimization' }
            },
            {
                name: 'Execute Rebalancing Trades',
                type: 'agent_task',
                config: { agentType: 'optimizer', task: 'execute_rebalancing' }
            },
            {
                name: 'Verify Results',
                type: 'agent_task',
                config: { agentType: 'validator', task: 'verify_portfolio' }
            }
        ]
    });
    
    // DeFi Yield Farming Workflow
    workflowTemplates.set('yield-farming', {
        id: 'yield-farming',
        name: 'DeFi Yield Farming',
        description: 'Automated yield farming optimization',
        category: 'defi',
        steps: [
            {
                name: 'Scan Yield Opportunities',
                type: 'data_processing',
                config: { processingType: 'yield_analysis' }
            },
            {
                name: 'Risk Analysis',
                type: 'ai_analysis',
                config: { analysisType: 'defi_risk_assessment' }
            },
            {
                name: 'Optimize Allocation',
                type: 'agent_task',
                config: { agentType: 'optimizer', task: 'optimize_yield' }
            },
            {
                name: 'Execute Farming',
                type: 'wallet_operation',
                config: { operation: 'stake_tokens' }
            }
        ]
    });
    
    logger.info(`Initialized ${workflowTemplates.size} workflow templates`);
}

// Start server
app.listen(PORT, () => {
    logger.info(`Workflow Orchestrator running on port ${PORT}`);
    console.log(`🔄 Workflow Orchestrator running on port ${PORT}`);
    console.log(`📋 Templates: ${workflowTemplates.size}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
