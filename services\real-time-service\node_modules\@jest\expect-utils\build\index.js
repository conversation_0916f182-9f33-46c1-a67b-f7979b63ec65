'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
var _exportNames = {
  equals: true,
  isA: true
};
Object.defineProperty(exports, 'equals', {
  enumerable: true,
  get: function () {
    return _jasmineUtils.equals;
  }
});
Object.defineProperty(exports, 'isA', {
  enumerable: true,
  get: function () {
    return _jasmineUtils.isA;
  }
});
var _jasmineUtils = require('./jasmineUtils');
var _utils = require('./utils');
Object.keys(_utils).forEach(function (key) {
  if (key === 'default' || key === '__esModule') return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _utils[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _utils[key];
    }
  });
});
