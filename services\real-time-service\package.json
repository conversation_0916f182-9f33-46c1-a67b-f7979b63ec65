{"name": "kontour-realtime-service", "version": "1.0.0", "description": "Kontour Coin Real-time WebSocket Service", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "socket.io": "^4.8.1", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "keywords": ["realtime", "websocket", "kontour", "cryptocurrency", "streaming"], "author": "Kontour Team", "license": "MIT"}