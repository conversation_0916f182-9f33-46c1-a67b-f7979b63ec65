{"name": "resolve-cwd", "version": "3.0.0", "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "license": "MIT", "repository": "sindresorhus/resolve-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "dependencies": {"resolve-from": "^5.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}