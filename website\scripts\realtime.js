// Real-time WebSocket Integration for Kontour Coin

class KontourRealTime {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.subscriptions = new Set();
        this.eventHandlers = new Map();
        
        this.init();
    }
    
    init() {
        this.connect();
        this.setupEventHandlers();
    }
    
    connect() {
        try {
            // Try to connect to real-time service
            this.socket = io('http://localhost:8035', {
                transports: ['websocket', 'polling'],
                timeout: 5000
            });
            
            this.socket.on('connect', () => {
                console.log('🔄 Connected to Kontour Real-time Service');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.showConnectionStatus('connected');
                
                // Resubscribe to channels
                this.resubscribe();
            });
            
            this.socket.on('disconnect', () => {
                console.log('❌ Disconnected from Real-time Service');
                this.isConnected = false;
                this.showConnectionStatus('disconnected');
            });
            
            this.socket.on('connect_error', (error) => {
                console.warn('⚠️ Real-time service unavailable, using mock data');
                this.isConnected = false;
                this.showConnectionStatus('error');
                this.startMockDataMode();
            });
            
            this.socket.on('reconnect_attempt', () => {
                this.reconnectAttempts++;
                console.log(`🔄 Reconnection attempt ${this.reconnectAttempts}`);
            });
            
        } catch (error) {
            console.warn('⚠️ WebSocket not available, using mock data mode');
            this.startMockDataMode();
        }
    }
    
    setupEventHandlers() {
        if (!this.socket) return;
        
        // Market data updates
        this.socket.on('market_data', (data) => {
            this.handleMarketData(data);
        });
        
        this.socket.on('market_update', (data) => {
            this.handleMarketUpdate(data);
        });
        
        // Wallet updates
        this.socket.on('wallet_data', (data) => {
            this.handleWalletData(data);
        });
        
        this.socket.on('wallet_update', (data) => {
            this.handleWalletUpdate(data);
        });
        
        // Agent status updates
        this.socket.on('agent_status', (data) => {
            this.handleAgentStatus(data);
        });
        
        this.socket.on('agent_update', (data) => {
            this.handleAgentUpdate(data);
        });
        
        // Workflow updates
        this.socket.on('workflow_updates', (data) => {
            this.handleWorkflowUpdates(data);
        });
        
        this.socket.on('workflow_update', (data) => {
            this.handleWorkflowUpdate(data);
        });
        
        // Swap updates
        this.socket.on('swap_executed', (data) => {
            this.handleSwapExecuted(data);
        });
        
        this.socket.on('swap_completed', (data) => {
            this.handleSwapCompleted(data);
        });
        
        // System status
        this.socket.on('system_status', (data) => {
            this.handleSystemStatus(data);
        });
    }
    
    subscribe(channel, symbol = 'all') {
        if (this.socket && this.isConnected) {
            this.socket.emit('subscribe', { channel, symbol });
            this.subscriptions.add(`${channel}:${symbol}`);
        }
    }
    
    unsubscribe(channel, symbol = 'all') {
        if (this.socket && this.isConnected) {
            this.socket.emit('unsubscribe', { channel, symbol });
            this.subscriptions.delete(`${channel}:${symbol}`);
        }
    }
    
    resubscribe() {
        this.subscriptions.forEach(subscription => {
            const [channel, symbol] = subscription.split(':');
            this.socket.emit('subscribe', { channel, symbol });
        });
    }
    
    // Event handlers
    handleMarketData(data) {
        this.updateMarketDisplay(data);
        this.triggerEvent('market_data', data);
    }
    
    handleMarketUpdate(data) {
        this.updateMarketDisplay(data);
        this.triggerEvent('market_update', data);
    }
    
    handleWalletData(data) {
        this.updateWalletDisplay(data);
        this.triggerEvent('wallet_data', data);
    }
    
    handleWalletUpdate(data) {
        this.updateWalletDisplay(data);
        this.triggerEvent('wallet_update', data);
    }
    
    handleAgentStatus(data) {
        this.updateAgentDisplay(data);
        this.triggerEvent('agent_status', data);
    }
    
    handleAgentUpdate(data) {
        this.updateAgentDisplay(data);
        this.triggerEvent('agent_update', data);
    }
    
    handleWorkflowUpdates(data) {
        this.updateWorkflowDisplay(data);
        this.triggerEvent('workflow_updates', data);
    }
    
    handleWorkflowUpdate(data) {
        this.updateWorkflowDisplay(data);
        this.triggerEvent('workflow_update', data);
    }
    
    handleSwapExecuted(data) {
        this.showSwapNotification(data, 'executed');
        this.triggerEvent('swap_executed', data);
    }
    
    handleSwapCompleted(data) {
        this.showSwapNotification(data, 'completed');
        this.triggerEvent('swap_completed', data);
    }
    
    handleSystemStatus(data) {
        this.updateSystemStatus(data);
        this.triggerEvent('system_status', data);
    }
    
    // UI Update methods
    updateMarketDisplay(data) {
        Object.entries(data).forEach(([symbol, tokenData]) => {
            // Update price displays
            const priceElements = document.querySelectorAll(`[data-token="${symbol}"] .price`);
            priceElements.forEach(el => {
                if (el) {
                    el.textContent = `$${tokenData.price}`;
                    el.classList.add('price-update');
                    setTimeout(() => el.classList.remove('price-update'), 1000);
                }
            });
            
            // Update change displays
            const changeElements = document.querySelectorAll(`[data-token="${symbol}"] .change`);
            changeElements.forEach(el => {
                if (el) {
                    const change = parseFloat(tokenData.change24h);
                    el.textContent = `${change > 0 ? '+' : ''}${change.toFixed(2)}%`;
                    el.className = `change ${change > 0 ? 'positive' : 'negative'}`;
                }
            });
        });
    }
    
    updateWalletDisplay(data) {
        // Update total value
        const totalValueEl = document.getElementById('total-value');
        if (totalValueEl) {
            totalValueEl.textContent = `$${data.totalValue}`;
            totalValueEl.classList.add('value-update');
            setTimeout(() => totalValueEl.classList.remove('value-update'), 1000);
        }
        
        // Update 24h change
        const changeEl = document.getElementById('value-change');
        if (changeEl) {
            const change = parseFloat(data.change24h);
            changeEl.textContent = `${change > 0 ? '+' : ''}${change.toFixed(2)}%`;
            changeEl.className = `value-change ${change > 0 ? 'positive' : 'negative'}`;
        }
        
        // Update individual balances
        if (data.balances) {
            Object.entries(data.balances).forEach(([token, balance]) => {
                const balanceEl = document.querySelector(`[data-token="${token}"] .balance-amount`);
                if (balanceEl) {
                    balanceEl.textContent = `${parseFloat(balance).toFixed(4)} ${token}`;
                }
            });
        }
    }
    
    updateAgentDisplay(data) {
        const agentsList = document.getElementById('agents-list');
        if (!agentsList || !Array.isArray(data)) return;
        
        agentsList.innerHTML = data.map(agent => `
            <div class="agent-card" data-agent="${agent.id}">
                <div class="agent-header">
                    <h4>${agent.id}</h4>
                    <span class="status-badge status-${agent.status}">${agent.status}</span>
                </div>
                <div class="agent-details">
                    <p>Type: ${agent.type}</p>
                    <p>Performance: ${(parseFloat(agent.performance) * 100).toFixed(1)}%</p>
                    <p>Tasks: ${agent.tasksCompleted}</p>
                </div>
            </div>
        `).join('');
    }
    
    updateWorkflowDisplay(data) {
        // Update workflow metrics
        const metricsMap = {
            'active-workflows': data.activeWorkflows,
            'completed-workflows': data.completedToday,
            'avg-execution-time': data.averageExecutionTime,
            'success-rate': data.successRate
        };
        
        Object.entries(metricsMap).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.classList.add('metric-update');
                setTimeout(() => element.classList.remove('metric-update'), 1000);
            }
        });
    }
    
    updateSystemStatus(data) {
        // Update service status indicators
        if (data.services) {
            Object.entries(data.services).forEach(([service, status]) => {
                const statusEl = document.querySelector(`[data-service="${service}"] .status`);
                if (statusEl) {
                    statusEl.className = `status status-${status}`;
                    statusEl.textContent = status;
                }
            });
        }
        
        // Update performance metrics
        if (data.performance) {
            Object.entries(data.performance).forEach(([metric, value]) => {
                const metricEl = document.getElementById(`${metric}-usage`);
                if (metricEl) {
                    metricEl.textContent = `${Math.round(value)}%`;
                }
            });
        }
    }
    
    showSwapNotification(data, type) {
        const message = type === 'executed' 
            ? `Swap initiated: ${data.fromAmount} ${data.fromToken} → ${data.toAmount} ${data.toToken}`
            : `Swap completed: ${data.fromAmount} ${data.fromToken} → ${data.toAmount} ${data.toToken}`;
        
        this.showNotification(message, type === 'completed' ? 'success' : 'info');
    }
    
    showConnectionStatus(status) {
        const statusEl = document.getElementById('connection-status');
        if (statusEl) {
            statusEl.className = `connection-status ${status}`;
            statusEl.textContent = status === 'connected' ? '🟢 Live' : 
                                 status === 'disconnected' ? '🟡 Reconnecting' : '🔴 Offline';
        }
    }
    
    showNotification(message, type = 'info') {
        // Use existing notification system
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    // Mock data mode for when WebSocket is unavailable
    startMockDataMode() {
        console.log('📊 Starting mock data mode');
        
        // Generate mock market updates
        setInterval(() => {
            const mockMarketData = this.generateMockMarketData();
            this.handleMarketData(mockMarketData);
        }, 5000);
        
        // Generate mock wallet updates
        setInterval(() => {
            const mockWalletData = this.generateMockWalletData();
            this.handleWalletData(mockWalletData);
        }, 10000);
        
        // Generate mock agent updates
        setInterval(() => {
            const mockAgentData = this.generateMockAgentData();
            this.handleAgentStatus(mockAgentData);
        }, 15000);
    }
    
    generateMockMarketData() {
        const tokens = ['KONTOUR', 'ETH', 'BTC', 'USDC', 'USDT', 'SOL'];
        const data = {};
        
        tokens.forEach(token => {
            const change = (Math.random() - 0.5) * 5;
            data[token] = {
                symbol: token,
                price: this.getBasePrice(token) * (1 + change / 100),
                change24h: change.toFixed(2),
                timestamp: new Date().toISOString()
            };
        });
        
        return data;
    }
    
    generateMockWalletData() {
        return {
            totalValue: (24567.89 + (Math.random() - 0.5) * 500).toFixed(2),
            change24h: ((Math.random() - 0.5) * 5).toFixed(2),
            balances: {
                KONTOUR: (1000 + Math.random() * 50).toFixed(4),
                ETH: (2.5 + Math.random() * 0.2).toFixed(4),
                BTC: (0.1 + Math.random() * 0.02).toFixed(6)
            }
        };
    }
    
    generateMockAgentData() {
        return [
            { id: 'coordinator-1', type: 'coordinator', status: 'active', performance: Math.random(), tasksCompleted: Math.floor(Math.random() * 50) + 100 },
            { id: 'specialist-1', type: 'specialist', status: 'active', performance: Math.random(), tasksCompleted: Math.floor(Math.random() * 50) + 150 }
        ];
    }
    
    getBasePrice(token) {
        const prices = {
            'KONTOUR': 75.42,
            'ETH': 2456.78,
            'BTC': 43567.89,
            'USDC': 1.00,
            'USDT': 0.999,
            'SOL': 98.76
        };
        return prices[token] || 1;
    }
    
    // Event system
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    triggerEvent(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
    
    // Public API methods
    executeSwap(swapData) {
        if (this.socket && this.isConnected) {
            this.socket.emit('execute_swap', swapData);
        } else {
            // Mock swap execution
            setTimeout(() => {
                this.handleSwapExecuted({
                    ...swapData,
                    swapId: `mock_${Date.now()}`,
                    status: 'pending'
                });
            }, 1000);
        }
    }
    
    createWorkflow(workflowData) {
        if (this.socket && this.isConnected) {
            this.socket.emit('create_workflow', workflowData);
        }
    }
    
    sendAgentCommand(agentId, command, parameters = {}) {
        if (this.socket && this.isConnected) {
            this.socket.emit('agent_command', { agentId, command, parameters });
        }
    }
}

// Initialize real-time connection
let kontourRealTime;

document.addEventListener('DOMContentLoaded', function() {
    kontourRealTime = new KontourRealTime();
    
    // Subscribe to relevant channels based on current page
    if (document.getElementById('wallet')) {
        kontourRealTime.subscribe('wallet');
        kontourRealTime.subscribe('market');
        kontourRealTime.subscribe('swaps');
    }
    
    if (document.getElementById('agents-list')) {
        kontourRealTime.subscribe('agents');
    }
    
    if (document.getElementById('dashboard')) {
        kontourRealTime.subscribe('workflows');
        kontourRealTime.subscribe('market');
    }
    
    // Always subscribe to system status
    kontourRealTime.subscribe('system');
});

// Export for global access
window.kontourRealTime = kontourRealTime;
