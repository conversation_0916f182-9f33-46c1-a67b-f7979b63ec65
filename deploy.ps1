# Kontour Coin Deployment Script for Windows PowerShell
Write-Host "🚀 Starting Kontour Coin Platform Deployment..." -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Node.js is installed
try {
    $nodeVersion = node -v
    Write-Success "Node.js $nodeVersion detected"
} catch {
    Write-Error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
}

# Install dependencies
Write-Status "Installing dependencies..."
npm install --legacy-peer-deps

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to install dependencies"
    exit 1
}

Write-Success "Dependencies installed successfully"

# Create static build
Write-Status "Creating static build..."

# Create out directory
if (Test-Path "out") {
    Remove-Item -Recurse -Force "out"
}
New-Item -ItemType Directory -Path "out" | Out-Null

# Copy static HTML files
Write-Status "Copying static files..."
Copy-Item "index.html" "out/" -ErrorAction SilentlyContinue
Copy-Item "sitemap.html" "out/" -ErrorAction SilentlyContinue
Copy-Item "pathway-flowchart.html" "out/" -ErrorAction SilentlyContinue
Copy-Item "demo-dashboard.html" "out/" -ErrorAction SilentlyContinue
Copy-Item "deployment-status.html" "out/" -ErrorAction SilentlyContinue

# Copy public directory if it exists
if (Test-Path "public") {
    Copy-Item "public\*" "out\" -Recurse -ErrorAction SilentlyContinue
}

# Create a simple package.json for the static site
$packageJson = @"
{
  "name": "kontour-coin-static",
  "version": "1.0.0",
  "description": "Kontour Coin Static Website",
  "main": "index.html",
  "scripts": {
    "start": "serve .",
    "dev": "serve ."
  }
}
"@

$packageJson | Out-File -FilePath "out\package.json" -Encoding UTF8

Write-Success "Static build created in 'out' directory"

# Deployment options
Write-Host ""
Write-Host "🌐 Deployment Options:" -ForegroundColor Cyan
Write-Host "1. Local Server (serve)"
Write-Host "2. Docker Container"
Write-Host "3. GitHub Pages"
Write-Host "4. Netlify"
Write-Host "5. Vercel"
Write-Host "6. Open in Browser"
Write-Host "7. Exit"

$choice = Read-Host "Choose deployment option (1-7)"

switch ($choice) {
    "1" {
        Write-Status "Starting local server..."
        try {
            serve --version | Out-Null
        } catch {
            Write-Status "Installing serve globally..."
            npm install -g serve
        }
        Set-Location "out"
        Write-Success "Server starting at http://localhost:3000"
        serve -s . -l 3000
    }
    "2" {
        Write-Status "Building Docker container..."
        docker build -t kontour-coin .
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker image built successfully"
            Write-Status "Starting container..."
            docker run -p 3000:3000 kontour-coin
        } else {
            Write-Error "Docker build failed"
        }
    }
    "3" {
        Write-Status "Preparing for GitHub Pages deployment..."
        Write-Warning "Make sure to:"
        Write-Host "1. Push your code to GitHub"
        Write-Host "2. Go to repository Settings > Pages"
        Write-Host "3. Select 'Deploy from a branch'"
        Write-Host "4. Choose 'main' branch and '/out' folder"
        Write-Host "5. Or run: npm run deploy:github"
    }
    "4" {
        Write-Status "Preparing for Netlify deployment..."
        try {
            netlify --version | Out-Null
            netlify deploy --dir=out --prod
        } catch {
            Write-Warning "Netlify CLI not found. Install with: npm install -g netlify-cli"
            Write-Status "Or drag and drop the 'out' folder to https://app.netlify.com/drop"
        }
    }
    "5" {
        Write-Status "Preparing for Vercel deployment..."
        try {
            vercel --version | Out-Null
            vercel --prod
        } catch {
            Write-Warning "Vercel CLI not found. Install with: npm install -g vercel"
            Write-Status "Or drag and drop the 'out' folder to https://vercel.com/new"
        }
    }
    "6" {
        Write-Status "Opening website in browser..."
        $indexPath = Join-Path (Get-Location) "out\index.html"
        Start-Process $indexPath
        Write-Success "Website opened in default browser"
    }
    "7" {
        Write-Status "Deployment cancelled"
        exit 0
    }
    default {
        Write-Error "Invalid option"
        exit 1
    }
}

Write-Success "Deployment process completed!"
Write-Host ""
Write-Host "📁 Static files are available in the 'out' directory" -ForegroundColor Green
Write-Host "🌐 You can also manually deploy these files to any static hosting service" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 Available pages:" -ForegroundColor Cyan
Write-Host "   • index.html - Main landing page"
Write-Host "   • sitemap.html - Complete sitemap"
Write-Host "   • pathway-flowchart.html - Architecture flowchart"
Write-Host "   • demo-dashboard.html - System dashboard"
Write-Host "   • deployment-status.html - Deployment status"
