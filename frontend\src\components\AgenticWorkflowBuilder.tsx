import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface WorkflowNode {
  id: string;
  type: 'agent' | 'task' | 'decision' | 'data' | 'trigger';
  label: string;
  position: { x: number; y: number };
  data: {
    agentType?: string;
    taskType?: string;
    parameters?: Record<string, any>;
    conditions?: string[];
  };
  connections: string[];
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdAt: string;
  lastModified: string;
}

const AgenticWorkflowBuilder: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [isBuilding, setIsBuilding] = useState(false);

  const nodeTypes = [
    { type: 'agent', label: 'AI Agent', icon: '🤖', color: 'bg-blue-100 border-blue-300' },
    { type: 'task', label: 'Task', icon: '📋', color: 'bg-green-100 border-green-300' },
    { type: 'decision', label: 'Decision', icon: '🔀', color: 'bg-yellow-100 border-yellow-300' },
    { type: 'data', label: 'Data Source', icon: '📊', color: 'bg-purple-100 border-purple-300' },
    { type: 'trigger', label: 'Trigger', icon: '⚡', color: 'bg-red-100 border-red-300' }
  ];

  const agentTypes = [
    { type: 'coordinator', label: 'Coordinator Agent', capabilities: ['orchestration', 'delegation'] },
    { type: 'specialist', label: 'Specialist Agent', capabilities: ['analysis', 'processing'] },
    { type: 'validator', label: 'Validator Agent', capabilities: ['verification', 'quality_check'] },
    { type: 'optimizer', label: 'Optimizer Agent', capabilities: ['optimization', 'efficiency'] },
    { type: 'learner', label: 'Learner Agent', capabilities: ['learning', 'adaptation'] }
  ];

  const createNewWorkflow = () => {
    const newWorkflow: Workflow = {
      id: `workflow_${Date.now()}`,
      name: `New Workflow ${workflows.length + 1}`,
      description: 'A new agentic workflow',
      nodes: [],
      status: 'draft',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };
    setWorkflows([...workflows, newWorkflow]);
    setSelectedWorkflow(newWorkflow);
    setIsBuilding(true);
  };

  const addNodeToWorkflow = (nodeType: string, position: { x: number; y: number }) => {
    if (!selectedWorkflow) return;

    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type: nodeType as any,
      label: `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} Node`,
      position,
      data: {},
      connections: []
    };

    const updatedWorkflow = {
      ...selectedWorkflow,
      nodes: [...selectedWorkflow.nodes, newNode],
      lastModified: new Date().toISOString()
    };

    setSelectedWorkflow(updatedWorkflow);
    setWorkflows(workflows.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w));
  };

  const connectNodes = (fromNodeId: string, toNodeId: string) => {
    if (!selectedWorkflow) return;

    const updatedNodes = selectedWorkflow.nodes.map(node => {
      if (node.id === fromNodeId && !node.connections.includes(toNodeId)) {
        return { ...node, connections: [...node.connections, toNodeId] };
      }
      return node;
    });

    const updatedWorkflow = {
      ...selectedWorkflow,
      nodes: updatedNodes,
      lastModified: new Date().toISOString()
    };

    setSelectedWorkflow(updatedWorkflow);
    setWorkflows(workflows.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w));
  };

  const deployWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch('/api/agentic/workflows/deploy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflow_id: workflowId,
          workflow: selectedWorkflow
        })
      });

      if (response.ok) {
        const updatedWorkflow = { ...selectedWorkflow!, status: 'active' as const };
        setSelectedWorkflow(updatedWorkflow);
        setWorkflows(workflows.map(w => w.id === workflowId ? updatedWorkflow : w));
      }
    } catch (error) {
      console.error('Error deploying workflow:', error);
    }
  };

  const getNodeIcon = (type: string) => {
    const nodeType = nodeTypes.find(nt => nt.type === type);
    return nodeType?.icon || '📦';
  };

  const getNodeColor = (type: string) => {
    const nodeType = nodeTypes.find(nt => nt.type === type);
    return nodeType?.color || 'bg-gray-100 border-gray-300';
  };

  const handleDragStart = (nodeType: string) => {
    setDraggedNode(nodeType);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedNode) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    addNodeToWorkflow(draggedNode, position);
    setDraggedNode(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">🔧 Agentic Workflow Builder</h1>
        <div className="space-x-2">
          <Button onClick={createNewWorkflow}>Create New Workflow</Button>
          {selectedWorkflow && (
            <Button 
              onClick={() => deployWorkflow(selectedWorkflow.id)}
              variant="outline"
              disabled={selectedWorkflow.status === 'active'}
            >
              {selectedWorkflow.status === 'active' ? 'Deployed' : 'Deploy Workflow'}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Workflow List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Workflows</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {workflows.map(workflow => (
                <div
                  key={workflow.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedWorkflow?.id === workflow.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => {
                    setSelectedWorkflow(workflow);
                    setIsBuilding(true);
                  }}
                >
                  <div className="font-medium">{workflow.name}</div>
                  <div className="text-sm text-gray-600">{workflow.nodes.length} nodes</div>
                  <Badge 
                    className={`mt-1 ${
                      workflow.status === 'active' ? 'bg-green-100 text-green-800' :
                      workflow.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {workflow.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Node Palette */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Node Palette</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700 mb-2">Basic Nodes</div>
              {nodeTypes.map(nodeType => (
                <div
                  key={nodeType.type}
                  className={`p-3 border-2 border-dashed rounded-lg cursor-move transition-colors ${nodeType.color}`}
                  draggable
                  onDragStart={() => handleDragStart(nodeType.type)}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{nodeType.icon}</span>
                    <span className="text-sm font-medium">{nodeType.label}</span>
                  </div>
                </div>
              ))}

              <div className="text-sm font-medium text-gray-700 mt-4 mb-2">Agent Types</div>
              {agentTypes.map(agentType => (
                <div
                  key={agentType.type}
                  className="p-2 border rounded-lg bg-blue-50 border-blue-200 cursor-move"
                  draggable
                  onDragStart={() => handleDragStart('agent')}
                >
                  <div className="text-sm font-medium">{agentType.label}</div>
                  <div className="text-xs text-gray-600">
                    {agentType.capabilities.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Workflow Canvas */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>
              {selectedWorkflow ? `${selectedWorkflow.name} - Canvas` : 'Select a Workflow'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedWorkflow ? (
              <div
                className="relative w-full h-96 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                {selectedWorkflow.nodes.length === 0 ? (
                  <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                    Drag nodes from the palette to build your workflow
                  </div>
                ) : (
                  selectedWorkflow.nodes.map(node => (
                    <div
                      key={node.id}
                      className={`absolute p-3 border-2 rounded-lg cursor-pointer ${getNodeColor(node.type)}`}
                      style={{
                        left: node.position.x,
                        top: node.position.y,
                        transform: 'translate(-50%, -50%)'
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getNodeIcon(node.type)}</span>
                        <div>
                          <div className="text-sm font-medium">{node.label}</div>
                          <div className="text-xs text-gray-600">{node.type}</div>
                        </div>
                      </div>
                      {node.connections.length > 0 && (
                        <div className="mt-1 text-xs text-blue-600">
                          → {node.connections.length} connection(s)
                        </div>
                      )}
                    </div>
                  ))
                )}

                {/* Connection Lines */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none">
                  {selectedWorkflow.nodes.map(node =>
                    node.connections.map(connectionId => {
                      const targetNode = selectedWorkflow.nodes.find(n => n.id === connectionId);
                      if (!targetNode) return null;

                      return (
                        <line
                          key={`${node.id}-${connectionId}`}
                          x1={node.position.x}
                          y1={node.position.y}
                          x2={targetNode.position.x}
                          y2={targetNode.position.y}
                          stroke="#3B82F6"
                          strokeWidth="2"
                          markerEnd="url(#arrowhead)"
                        />
                      );
                    })
                  )}
                  <defs>
                    <marker
                      id="arrowhead"
                      markerWidth="10"
                      markerHeight="7"
                      refX="9"
                      refY="3.5"
                      orient="auto"
                    >
                      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6" />
                    </marker>
                  </defs>
                </svg>
              </div>
            ) : (
              <div className="flex items-center justify-center h-96 text-gray-500">
                Create or select a workflow to start building
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Workflow Properties */}
      {selectedWorkflow && (
        <Card>
          <CardHeader>
            <CardTitle>Workflow Properties</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={selectedWorkflow.name}
                  onChange={(e) => {
                    const updated = { ...selectedWorkflow, name: e.target.value };
                    setSelectedWorkflow(updated);
                    setWorkflows(workflows.map(w => w.id === updated.id ? updated : w));
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <Badge className={`${
                  selectedWorkflow.status === 'active' ? 'bg-green-100 text-green-800' :
                  selectedWorkflow.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedWorkflow.status}
                </Badge>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nodes</label>
                <div className="text-lg font-semibold">{selectedWorkflow.nodes.length}</div>
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={selectedWorkflow.description}
                onChange={(e) => {
                  const updated = { ...selectedWorkflow, description: e.target.value };
                  setSelectedWorkflow(updated);
                  setWorkflows(workflows.map(w => w.id === updated.id ? updated : w));
                }}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AgenticWorkflowBuilder;
