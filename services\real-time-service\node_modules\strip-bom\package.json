{"name": "strip-bom", "version": "4.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string", "license": "MIT", "repository": "sindresorhus/strip-bom", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}