{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "deprecatedAssertSyntax", "assertVersion", "Error", "name", "manipulateOptions", "parserOpts", "generatorOpts", "_generatorOpts$import", "importAttributesKeyword", "importAssertionsPluginIndex", "plugins", "indexOf", "splice", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  deprecatedAssertSyntax?: boolean;\n}\n\nexport default declare((api, { deprecatedAssertSyntax }: Options) => {\n  api.assertVersion(REQUIRED_VERSION(\"^7.22.0\"));\n\n  if (\n    deprecatedAssertSyntax != null &&\n    typeof deprecatedAssertSyntax !== \"boolean\"\n  ) {\n    throw new Error(\n      \"'deprecatedAssertSyntax' must be a boolean, if specified.\",\n    );\n  }\n\n  return {\n    name: \"syntax-import-attributes\",\n\n    manipulateOptions({ parserOpts, generatorOpts }) {\n      generatorOpts.importAttributesKeyword ??= \"with\";\n\n      const importAssertionsPluginIndex =\n        parserOpts.plugins.indexOf(\"importAssertions\");\n      if (importAssertionsPluginIndex !== -1) {\n        parserOpts.plugins.splice(importAssertionsPluginIndex, 1);\n        deprecatedAssertSyntax = true;\n      }\n\n      if (deprecatedAssertSyntax) {\n        parserOpts.plugins.push(\"deprecatedImportAssert\", [\n          \"importAttributes\",\n          { deprecatedAssertSyntax: true },\n        ]);\n      } else {\n        parserOpts.plugins.push(\"importAttributes\");\n      }\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAE;EAAEC;AAAgC,CAAC,KAAK;EACnED,GAAG,CAACE,aAAa,sCAA4B,CAAC;EAE9C,IACED,sBAAsB,IAAI,IAAI,IAC9B,OAAOA,sBAAsB,KAAK,SAAS,EAC3C;IACA,MAAM,IAAIE,KAAK,CACb,2DACF,CAAC;EACH;EAEA,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAEhCC,iBAAiBA,CAAC;MAAEC,UAAU;MAAEC;IAAc,CAAC,EAAE;MAAA,IAAAC,qBAAA;MAC/C,CAAAA,qBAAA,GAAAD,aAAa,CAACE,uBAAuB,YAAAD,qBAAA,GAArCD,aAAa,CAACE,uBAAuB,GAAK,MAAM;MAEhD,MAAMC,2BAA2B,GAC/BJ,UAAU,CAACK,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAChD,IAAIF,2BAA2B,KAAK,CAAC,CAAC,EAAE;QACtCJ,UAAU,CAACK,OAAO,CAACE,MAAM,CAACH,2BAA2B,EAAE,CAAC,CAAC;QACzDT,sBAAsB,GAAG,IAAI;MAC/B;MAEA,IAAIA,sBAAsB,EAAE;QAC1BK,UAAU,CAACK,OAAO,CAACG,IAAI,CAAC,wBAAwB,EAAE,CAChD,kBAAkB,EAClB;UAAEb,sBAAsB,EAAE;QAAK,CAAC,CACjC,CAAC;MACJ,CAAC,MAAM;QACLK,UAAU,CAACK,OAAO,CAACG,IAAI,CAAC,kBAAkB,CAAC;MAC7C;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}