# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [3.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@3.0.0) (2019-12-20)

**Note:** Version bump only for package istanbul-lib-report





# [3.0.0-alpha.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@3.0.0-alpha.2) (2019-12-07)

**Note:** Version bump only for package istanbul-lib-report





# [3.0.0-alpha.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@3.0.0-alpha.1) (2019-10-06)

**Note:** Version bump only for package istanbul-lib-report





# [3.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@3.0.0-alpha.0) (2019-06-19)


### Bug Fixes

* **package:** update supports-color to version 7.0.0 ([#420](https://github.com/istanbuljs/istanbuljs/issues/420)) ([631029d](https://github.com/istanbuljs/istanbuljs/commit/631029d))
* Properly combine directories in nested summarizer ([#380](https://github.com/istanbuljs/istanbuljs/issues/380)) ([50afdbb](https://github.com/istanbuljs/istanbuljs/commit/50afdbb))


### Features

* Refactor istanbul-lib-report so report can choose summarizer ([#408](https://github.com/istanbuljs/istanbuljs/issues/408)) ([0f328fd](https://github.com/istanbuljs/istanbuljs/commit/0f328fd))
* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Existing istanbul-lib-report API's have been changed
* Node.js 8 is now required





## [3.0.1](https://github.com/istanbuljs/istanbuljs/compare/istanbul-lib-report-v3.0.0...istanbul-lib-report-v3.0.1) (2023-07-25)


### Bug Fixes

* **deps:** update make-dir CVE-2022-25883 ([eab82a9](https://github.com/istanbuljs/istanbuljs/commit/eab82a9aeff140a8fd2981c7f872830c985d479f))

## [2.0.8](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.8) (2019-04-24)

**Note:** Version bump only for package istanbul-lib-report





## [2.0.7](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.7) (2019-04-09)

**Note:** Version bump only for package istanbul-lib-report





## [2.0.6](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.6) (2019-04-03)


### Bug Fixes

* Avoid corrupting HTML report's arrow png during copy ([#343](https://github.com/istanbuljs/istanbuljs/issues/343)) ([ce664c7](https://github.com/istanbuljs/istanbuljs/commit/ce664c7))





## [2.0.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.5) (2019-03-12)

**Note:** Version bump only for package istanbul-lib-report





## [2.0.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.4) (2019-01-26)


### Bug Fixes

* nested summarizer error with no files ([#230](https://github.com/istanbuljs/istanbuljs/issues/230)) ([07724bf](https://github.com/istanbuljs/istanbuljs/commit/07724bf))





<a name="2.0.3"></a>
## [2.0.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.3) (2018-12-25)




**Note:** Version bump only for package istanbul-lib-report

<a name="2.0.2"></a>
## [2.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.2) (2018-09-06)




**Note:** Version bump only for package istanbul-lib-report

<a name="2.0.1"></a>
## [2.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.1) (2018-07-07)




**Note:** Version bump only for package istanbul-lib-report

<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@2.0.0) (2018-06-06)


### Bug Fixes

* use null prototype for map objects ([#177](https://github.com/istanbuljs/istanbuljs/issues/177)) ([9a5a30c](https://github.com/istanbuljs/istanbuljs/commit/9a5a30c))


### BREAKING CHANGES

* a null prototype is now used in several places rather than the default `{}` assignment.




<a name="1.1.4"></a>
## [1.1.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@1.1.4) (2018-03-04)




**Note:** Version bump only for package istanbul-lib-report

<a name="1.1.3"></a>
## [1.1.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@1.1.3) (2018-02-13)




**Note:** Version bump only for package istanbul-lib-report

<a name="1.1.2"></a>
## [1.1.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@1.1.2) (2017-10-21)


### Bug Fixes

* remove call to mkdirp.sync() in constructor so when used for ConsoleWriter ([#104](https://github.com/istanbuljs/istanbuljs/issues/104)) ([58eb79d](https://github.com/istanbuljs/istanbuljs/commit/58eb79d))




<a name="1.1.1"></a>
## [1.1.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-report@1.1.1) (2017-05-27)




<a name="1.1.0"></a>
# [1.1.0](https://github.com/istanbuljs/istanbul-lib-report/compare/<EMAIL>-lib-report@1.1.0) (2017-04-29)


### Features

* once 100% line coverage is achieved, missing branch coverage is now shown in text report ([#45](https://github.com/istanbuljs/istanbuljs/issues/45)) ([8a809f8](https://github.com/istanbuljs/istanbul-lib-report/commit/8a809f8))




<a name="1.0.0"></a>
# [1.0.0](https://github.com/istanbuljs/istanbul-lib-report/compare/<EMAIL>-lib-report@1.0.0) (2017-03-27)
